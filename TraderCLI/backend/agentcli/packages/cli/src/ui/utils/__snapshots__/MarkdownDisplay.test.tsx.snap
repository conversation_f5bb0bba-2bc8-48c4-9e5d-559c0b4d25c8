// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`<MarkdownDisplay /> > correctly parses a mix of markdown elements 1`] = `
"Main Title

Here is a paragraph.

 - List item 1
 - List item 2

 1 some code

Another paragraph.
"
`;

exports[`<MarkdownDisplay /> > handles a table at the end of the input 1`] = `
"Some text before.
| A | B |
|---|
| 1 | 2 |"
`;

exports[`<MarkdownDisplay /> > handles unclosed (pending) code blocks 1`] = `" 1 let y = 2;"`;

exports[`<MarkdownDisplay /> > inserts a single space between paragraphs 1`] = `
"Paragraph 1.

Paragraph 2."
`;

exports[`<MarkdownDisplay /> > renders a fenced code block with a language 1`] = `
" 1 const x = 1;
 2 console.log(x);"
`;

exports[`<MarkdownDisplay /> > renders a fenced code block without a language 1`] = `" 1 plain text"`;

exports[`<MarkdownDisplay /> > renders a simple paragraph 1`] = `"Hello, world."`;

exports[`<MarkdownDisplay /> > renders headers with correct levels 1`] = `
"Header 1
Header 2
Header 3
Header 4
"
`;

exports[`<MarkdownDisplay /> > renders horizontal rules 1`] = `
"Hello
---
World
---
Test
"
`;

exports[`<MarkdownDisplay /> > renders nested unordered lists 1`] = `
" * Level 1
   * Level 2
     * Level 3
"
`;

exports[`<MarkdownDisplay /> > renders nothing for empty text 1`] = `""`;

exports[`<MarkdownDisplay /> > renders ordered lists 1`] = `
" 1. First item
 2. Second item
"
`;

exports[`<MarkdownDisplay /> > renders tables correctly 1`] = `
"
┌──────────┬──────────┐
│ Header 1 │ Header 2 │
├──────────┼──────────┤
│ Cell 1   │ Cell 2   │
│ Cell 3   │ Cell 4   │
└──────────┴──────────┘
"
`;

exports[`<MarkdownDisplay /> > renders unordered lists with different markers 1`] = `
" - item A
 * item B
 + item C
"
`;
