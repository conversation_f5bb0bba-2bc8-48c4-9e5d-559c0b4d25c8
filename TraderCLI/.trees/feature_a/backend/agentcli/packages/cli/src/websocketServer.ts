/**
 * WebSocket Server mode for Gemini CLI
 * Provides real-time interactive messaging with state management
 */

import { Server as SocketIOServer } from 'socket.io';
import { createServer } from 'http';
import { 
  Config,
  ToolRegistry,
  executeTool<PERSON>all,
  ToolCallRequestInfo
} from '@google/gemini-cli-core';
import {
  Content,
  Part
} from '@google/genai';
import { parseAndFormatApiError } from './ui/utils/errorParsing.js';

interface ClientMessage {
  message: string;
  conversationId?: string;
}

// Store conversation state per socket connection
const conversations = new Map<string, {
  messages: Content[];
  config: Config;
  toolRegistry: ToolRegistry;
  isProcessing: boolean;
}>();

export async function startWebSocketServer(config: Config, port: number = 3001) {
  console.log(`[WebSocket] Initializing WebSocket server...`);
  console.log(`[WebSocket] Config initialized:`, !!config);
  console.log(`[WebSocket] Config has getGeminiClient:`, typeof config.getGeminiClient === 'function');
  
  // Test config initialization - the config should already be initialized when passed in
  try {
    const testClient = config.getGeminiClient();
    console.log(`[WebSocket] GeminiClient test successful:`, !!testClient);
    if (!testClient) {
      console.error(`[WebSocket] ERROR: GeminiClient is null! Config was not properly initialized before being passed to WebSocket server.`);
      throw new Error('GeminiClient not initialized');
    }
  } catch (error) {
    console.error(`[WebSocket] ERROR: Failed to get GeminiClient:`, error);
    throw error;
  }

  const httpServer = createServer();
  const io = new SocketIOServer(httpServer, {
    cors: {
      origin: "*", // Configure based on your needs
      methods: ["GET", "POST"]
    },
    // Enable connection state recovery
    connectionStateRecovery: {
      maxDisconnectionDuration: 2 * 60 * 1000, // 2 minutes
      skipMiddlewares: true,
    }
  });

  console.log(`[WebSocket] Starting server on port ${port}...`);

  io.on('connection', async (socket) => {
    console.log(`[WebSocket] Client connected: ${socket.id}`);
    console.log(`[WebSocket] Connection recovered: ${socket.recovered}`);

    try {
      // Initialize conversation state for this connection
      console.log(`[WebSocket] Initializing socket config for ${socket.id}...`);
      const socketConfig = await initializeSocketConfig(config);
      
      console.log(`[WebSocket] Socket config initialized:`, {
        hasConfig: !!socketConfig.config,
        hasToolRegistry: !!socketConfig.toolRegistry,
        configType: socketConfig.config?.constructor?.name
      });
      
      conversations.set(socket.id, {
        messages: [],
        config: socketConfig.config,
        toolRegistry: socketConfig.toolRegistry,
        isProcessing: false
      });
      console.log(`[WebSocket] Conversation state created for ${socket.id}`);
    } catch (error) {
      console.error(`[WebSocket] ERROR initializing socket config:`, error);
      socket.emit('error', { error: 'Failed to initialize connection' });
      return;
    }

    // Send ready signal
    socket.emit('ready', { 
      model: config.getModel(),
      recovered: socket.recovered 
    });

    // Handle incoming messages
    socket.on('message', async (data: ClientMessage) => {
      console.log(`[WebSocket] Received message from ${socket.id}:`, data.message);
      
      const conversation = conversations.get(socket.id);
      if (!conversation) {
        console.error(`[WebSocket] ERROR: No conversation state found for ${socket.id}`);
        socket.emit('error', { error: 'No conversation state found' });
        return;
      }
      
      // Inject system context about file handles
      const enhancedMessage = `${data.message}

[SYSTEM CONTEXT]: You have access to a file handle system. When files are created or read, they are assigned handles like @F1, @F2, etc. You can use these handles directly in commands instead of full paths. For example: "python3 @F1" instead of "python3 /full/path/to/file.py". The system will automatically resolve these handles to the correct paths.

IMPORTANT: Execute the full workflow without stopping for confirmation at each step. Be proactive and complete the entire analysis.`;

      if (conversation.isProcessing) {
        console.warn(`[WebSocket] WARNING: Already processing a message for ${socket.id}`);
        socket.emit('error', { error: 'Already processing a message' });
        return;
      }

      try {
        conversation.isProcessing = true;
        console.log(`[WebSocket] Processing message for ${socket.id}...`);
        await processMessage(socket, conversation, enhancedMessage);
      } catch (error) {
        console.error(`[WebSocket] ERROR processing message:`, error);
        console.error(`[WebSocket] Error stack:`, error instanceof Error ? error.stack : 'No stack trace');
        socket.emit('error', { 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      } finally {
        conversation.isProcessing = false;
        console.log(`[WebSocket] Message processing complete for ${socket.id}`);
      }
    });

    // Handle disconnect
    socket.on('disconnect', (reason) => {
      console.log(`Client disconnected: ${socket.id}, reason: ${reason}`);
      // Clean up conversation state
      conversations.delete(socket.id);
    });

    // Handle clear conversation
    socket.on('clear', () => {
      const conversation = conversations.get(socket.id);
      if (conversation) {
        conversation.messages = [];
        socket.emit('cleared');
      }
    });
  });

  httpServer.on('error', (error: any) => {
    if (error.code === 'EADDRINUSE') {
      console.error(`[WebSocket] ERROR: Port ${port} is already in use`);
      console.error(`[WebSocket] Try killing the process: lsof -ti:${port} | xargs kill -9`);
    } else {
      console.error(`[WebSocket] Server error:`, error);
    }
    process.exit(1);
  });

  httpServer.listen(port, () => {
    console.log(`[WebSocket] Server successfully started on port ${port}`);
  });
}

async function initializeSocketConfig(baseConfig: Config) {
  // Use the already initialized base config
  // Each socket will share the same config instance but maintain separate conversation state
  try {
    console.log(`[initializeSocketConfig] Getting tool registry...`);
    const toolRegistry = await baseConfig.getToolRegistry();
    console.log(`[initializeSocketConfig] Tool registry obtained:`, !!toolRegistry);
    
    // Verify GeminiClient is accessible
    const geminiClient = baseConfig.getGeminiClient();
    console.log(`[initializeSocketConfig] GeminiClient accessible:`, !!geminiClient);
    
    return { config: baseConfig, toolRegistry };
  } catch (error) {
    console.error(`[initializeSocketConfig] ERROR:`, error);
    throw error;
  }
}

async function processMessage(
  socket: any,
  conversation: any,
  message: string
) {
  const { config, toolRegistry, messages } = conversation;
  
  console.log(`[processMessage] Starting processing...`);
  console.log(`[processMessage] Config exists:`, !!config);
  console.log(`[processMessage] ToolRegistry exists:`, !!toolRegistry);
  console.log(`[processMessage] Current message count:`, messages.length);
  
  // Add user message
  messages.push({ role: 'user', parts: [{ text: message }] });
  
  let geminiClient;
  try {
    console.log(`[processMessage] Getting GeminiClient...`);
    geminiClient = config.getGeminiClient();
    console.log(`[processMessage] GeminiClient obtained:`, !!geminiClient);
  } catch (error) {
    console.error(`[processMessage] ERROR getting GeminiClient:`, error);
    throw new Error(`Failed to get GeminiClient: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
  
  let chat;
  try {
    console.log(`[processMessage] Creating chat with ${messages.length - 1} previous messages...`);
    chat = await geminiClient.getChat(messages.slice(0, -1)); // Previous messages as history
    console.log(`[processMessage] Chat created successfully`);
  } catch (error) {
    console.error(`[processMessage] ERROR creating chat:`, error);
    throw new Error(`Failed to create chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
  
  try {
    // Send message to Gemini
    const responseStream = await chat.sendMessageStream({
      message: [{ text: message }],
      config: {
        tools: [
          { functionDeclarations: toolRegistry.getFunctionDeclarations() }
        ],
      }
    });

    let fullResponse = '';
    const functionCalls: any[] = [];

    // Stream the response
    for await (const chunk of responseStream) {
      const textPart = getResponseText(chunk);
      if (textPart) {
        fullResponse += textPart;
        socket.emit('response', { 
          type: 'stream', 
          content: textPart 
        });
      }
      
      if (chunk.functionCalls) {
        functionCalls.push(...chunk.functionCalls);
      }
    }

    // Handle function calls
    if (functionCalls.length > 0) {
      const toolResponseParts: Part[] = [];

      for (const fc of functionCalls) {
        socket.emit('response', { 
          type: 'tool', 
          tool: { name: fc.name, args: fc.args } 
        });

        const callId = fc.id ?? `${fc.name}-${Date.now()}`;
        const requestInfo: ToolCallRequestInfo = {
          callId,
          name: fc.name as string,
          args: (fc.args ?? {}) as Record<string, unknown>,
          isClientInitiated: false,
          prompt_id: socket.id
        };

        const toolResponse = await executeToolCall(
          config,
          requestInfo,
          toolRegistry,
          new AbortController().signal
        );

        if (toolResponse.error) {
          socket.emit('response', { 
            type: 'error', 
            error: `Tool error: ${toolResponse.error.message}` 
          });
        }

        if (toolResponse.responseParts) {
          const parts = Array.isArray(toolResponse.responseParts)
            ? toolResponse.responseParts
            : [toolResponse.responseParts];
          for (const part of parts) {
            if (typeof part === 'string') {
              toolResponseParts.push({ text: part });
            } else if (part) {
              toolResponseParts.push(part);
            }
          }
        }
      }

      // Get final response after tool execution
      if (toolResponseParts.length > 0) {
        messages.push({ 
          role: 'function', 
          parts: toolResponseParts 
        });

        const finalStream = await chat.sendMessageStream({
          message: toolResponseParts,
          config: {
            tools: [
              { functionDeclarations: toolRegistry.getFunctionDeclarations() }
            ],
          }
        });

        for await (const chunk of finalStream) {
          const textPart = getResponseText(chunk);
          if (textPart) {
            fullResponse += textPart;
            socket.emit('response', { 
              type: 'stream', 
              content: textPart 
            });
          }
        }
      }
    }

    // Add assistant response to conversation
    if (fullResponse) {
      messages.push({ 
        role: 'model', 
        parts: [{ text: fullResponse }] 
      });
    }

    // Send completion signal
    socket.emit('response', { type: 'complete' });

  } catch (error) {
    const errorMessage = parseAndFormatApiError(error);
    socket.emit('response', { 
      type: 'error', 
      error: errorMessage 
    });
    throw error;
  }
}

function getResponseText(response: any): string | null {
  if (response.candidates && response.candidates.length > 0) {
    const candidate = response.candidates[0];
    if (
      candidate.content &&
      candidate.content.parts &&
      candidate.content.parts.length > 0
    ) {
      // Filter out thought parts in headless mode
      const thoughtPart = candidate.content.parts[0];
      if (thoughtPart?.thought) {
        return null;
      }
      return candidate.content.parts
        .filter((part: any) => part.text)
        .map((part: any) => part.text)
        .join('');
    }
  }
  return null;
}