# .github/workflows/e2e.yml

name: E2E Tests

on:
  push:
    branches: [main]
  merge_group:

jobs:
  e2e-test:
    name: E2E Test - ${{ matrix.sandbox }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        sandbox: [sandbox:none, sandbox:docker]
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4

      - name: Set up Node.js
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4
        with:
          node-version: 20.x
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Set up Docker
        if: matrix.sandbox == 'sandbox:docker'
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3

      - name: Set up Podman
        if: matrix.sandbox == 'sandbox:podman'
        uses: redhat-actions/podman-login@4934294ad0449894bcd1e9f191899d7292469603 # v1
        with:
          registry: docker.io
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Run E2E tests
        env:
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
        run: npm run test:integration:${{ matrix.sandbox }} -- --verbose --keep-output
