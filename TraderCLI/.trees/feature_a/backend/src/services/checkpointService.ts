import { EventEmitter } from 'events';
import * as fs from 'fs/promises';
import * as path from 'path';
import { Config, Logger, GitService } from '@google/gemini-cli-core';
import { Content } from '@google/genai';

interface CheckpointData {
  history: any[];
  clientHistory: Content[];
  toolCall?: {
    name: string;
    args: Record<string, unknown>;
  };
  commitHash?: string;
  filePath?: string;
  timestamp: string;
  socketId: string;
  userId?: string;
}

export class CheckpointService extends EventEmitter {
  private config: Config;
  private gitService: GitService | null = null;
  private logger: Logger;
  private checkpointDir: string;

  constructor(config: Config) {
    super();
    this.config = config;
    this.logger = new Logger();
    
    // Initialize checkpoint directory
    const projectTempDir = config.getProjectTempDir();
    if (!projectTempDir) {
      throw new Error('Project temp directory not available');
    }
    
    this.checkpointDir = path.join(projectTempDir, 'checkpoints');
    this.initializeService();
  }

  private async initializeService() {
    try {
      // Create checkpoint directory
      await fs.mkdir(this.checkpointDir, { recursive: true });
      console.log(`[CheckpointService] Initialized checkpoint directory: ${this.checkpointDir}`);
      
      // Initialize Git service if checkpointing is enabled
      if (this.config.getCheckpointingEnabled()) {
        const GitServiceClass = (await import('@google/gemini-cli-core')).GitService;
        this.gitService = new GitServiceClass(this.logger);
        await this.gitService.initialize();
        console.log('[CheckpointService] Git service initialized for checkpointing');
      }
    } catch (error) {
      console.error('[CheckpointService] Initialization error:', error);
      throw error;
    }
  }

  /**
   * Create a checkpoint before file modification
   */
  async createCheckpoint(
    socketId: string,
    userId: string,
    history: any[],
    clientHistory: Content[],
    toolCall?: { name: string; args: Record<string, unknown> }
  ): Promise<string | null> {
    if (!this.config.getCheckpointingEnabled()) {
      console.log('[CheckpointService] Checkpointing is disabled');
      return null;
    }

    try {
      const timestamp = new Date()
        .toISOString()
        .replace(/:/g, '-')
        .replace(/\./g, '_');

      let checkpointName = `${timestamp}-session-${socketId}`;
      let commitHash: string | undefined;

      // If it's a file modification tool, create Git snapshot
      if (toolCall && (toolCall.name === 'write_file' || toolCall.name === 'replace')) {
        const filePath = toolCall.args['file_path'] as string;
        
        if (filePath && this.gitService) {
          // Create Git snapshot
          commitHash = await this.gitService.createFileSnapshot(
            `Checkpoint for ${toolCall.name} on ${path.basename(filePath)}`
          );
          
          if (!commitHash) {
            commitHash = await this.gitService.getCurrentCommitHash();
          }
          
          checkpointName = `${timestamp}-${path.basename(filePath)}-${toolCall.name}`;
        }
      }

      // Create checkpoint data
      const checkpointData: CheckpointData = {
        history,
        clientHistory,
        toolCall,
        commitHash,
        filePath: toolCall?.args?.['file_path'] as string,
        timestamp,
        socketId,
        userId
      };

      // Save checkpoint
      const checkpointPath = path.join(this.checkpointDir, `${checkpointName}.json`);
      await fs.writeFile(
        checkpointPath,
        JSON.stringify(checkpointData, null, 2)
      );

      console.log(`[CheckpointService] Created checkpoint: ${checkpointName}`);
      this.emit('checkpoint-created', { name: checkpointName, data: checkpointData });
      
      return checkpointName;
    } catch (error) {
      console.error('[CheckpointService] Failed to create checkpoint:', error);
      return null;
    }
  }

  /**
   * List available checkpoints for a user/socket
   */
  async listCheckpoints(socketId?: string, userId?: string): Promise<string[]> {
    try {
      const files = await fs.readdir(this.checkpointDir);
      const checkpointFiles = files.filter(f => f.endsWith('.json'));
      
      if (socketId || userId) {
        // Filter checkpoints by socketId or userId
        const filteredCheckpoints: string[] = [];
        
        for (const file of checkpointFiles) {
          try {
            const data = await fs.readFile(path.join(this.checkpointDir, file), 'utf-8');
            const checkpoint = JSON.parse(data) as CheckpointData;
            
            if ((socketId && checkpoint.socketId === socketId) || 
                (userId && checkpoint.userId === userId)) {
              filteredCheckpoints.push(file.replace('.json', ''));
            }
          } catch {
            // Skip invalid checkpoint files
          }
        }
        
        return filteredCheckpoints.sort().reverse(); // Most recent first
      }
      
      return checkpointFiles
        .map(f => f.replace('.json', ''))
        .sort()
        .reverse(); // Most recent first
    } catch (error) {
      console.error('[CheckpointService] Failed to list checkpoints:', error);
      return [];
    }
  }

  /**
   * Restore a checkpoint
   */
  async restoreCheckpoint(checkpointName: string): Promise<CheckpointData | null> {
    try {
      const checkpointPath = path.join(this.checkpointDir, `${checkpointName}.json`);
      const data = await fs.readFile(checkpointPath, 'utf-8');
      const checkpoint = JSON.parse(data) as CheckpointData;
      
      // Restore files if Git snapshot exists
      if (checkpoint.commitHash && this.gitService) {
        console.log(`[CheckpointService] Restoring files from commit: ${checkpoint.commitHash}`);
        await this.gitService.restoreFromCommit(checkpoint.commitHash);
      }
      
      console.log(`[CheckpointService] Restored checkpoint: ${checkpointName}`);
      this.emit('checkpoint-restored', { name: checkpointName, data: checkpoint });
      
      return checkpoint;
    } catch (error) {
      console.error('[CheckpointService] Failed to restore checkpoint:', error);
      return null;
    }
  }

  /**
   * Delete a checkpoint
   */
  async deleteCheckpoint(checkpointName: string): Promise<boolean> {
    try {
      const checkpointPath = path.join(this.checkpointDir, `${checkpointName}.json`);
      await fs.unlink(checkpointPath);
      
      console.log(`[CheckpointService] Deleted checkpoint: ${checkpointName}`);
      this.emit('checkpoint-deleted', { name: checkpointName });
      
      return true;
    } catch (error) {
      console.error('[CheckpointService] Failed to delete checkpoint:', error);
      return false;
    }
  }

  /**
   * Save conversation checkpoint (like /chat save)
   */
  async saveConversation(
    tag: string,
    socketId: string,
    userId: string,
    history: any[],
    clientHistory: Content[]
  ): Promise<string> {
    const checkpointName = `checkpoint-${tag}`;
    const checkpointData: CheckpointData = {
      history,
      clientHistory,
      timestamp: new Date().toISOString(),
      socketId,
      userId
    };

    const checkpointPath = path.join(this.checkpointDir, `${checkpointName}.json`);
    await fs.writeFile(
      checkpointPath,
      JSON.stringify(checkpointData, null, 2)
    );

    console.log(`[CheckpointService] Saved conversation: ${tag}`);
    return checkpointName;
  }

  /**
   * Load conversation checkpoint (like /chat resume)
   */
  async loadConversation(tag: string): Promise<CheckpointData | null> {
    const checkpointName = `checkpoint-${tag}`;
    return this.restoreCheckpoint(checkpointName);
  }

  /**
   * Clean up old checkpoints
   */
  async cleanupOldCheckpoints(daysOld: number = 7): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    
    let deletedCount = 0;
    
    try {
      const checkpoints = await this.listCheckpoints();
      
      for (const checkpointName of checkpoints) {
        try {
          const checkpointPath = path.join(this.checkpointDir, `${checkpointName}.json`);
          const data = await fs.readFile(checkpointPath, 'utf-8');
          const checkpoint = JSON.parse(data) as CheckpointData;
          
          if (new Date(checkpoint.timestamp) < cutoffDate) {
            await this.deleteCheckpoint(checkpointName);
            deletedCount++;
          }
        } catch {
          // Skip invalid checkpoint files
        }
      }
      
      console.log(`[CheckpointService] Cleaned up ${deletedCount} old checkpoints`);
    } catch (error) {
      console.error('[CheckpointService] Cleanup error:', error);
    }
    
    return deletedCount;
  }
}