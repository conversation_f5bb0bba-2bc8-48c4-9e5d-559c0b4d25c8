import * as fs from 'fs/promises';
import * as path from 'path';
import { EventEmitter } from 'events';

interface ConversationMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  fileHandles?: string[];
}

interface Session {
  id: string;
  messages: ConversationMessage[];
  fileHandles: Map<string, string>; // handleId -> absolutePath
  createdAt: Date;
  lastActivity: Date;
}

export class SessionService extends EventEmitter {
  private sessionsDir: string;
  private currentSession: Session | null = null;

  constructor() {
    super();
    // Store sessions in ~/.tradercli/sessions/
    const homeDir = process.env.HOME || process.env.USERPROFILE || '';
    this.sessionsDir = path.join(homeDir, '.tradercli', 'sessions');
    this.ensureSessionsDir();
  }

  private async ensureSessionsDir() {
    try {
      await fs.mkdir(this.sessionsDir, { recursive: true });
    } catch (error) {
      console.error('[SessionService] Failed to create sessions directory:', error);
    }
  }

  async createSession(): Promise<string> {
    const sessionId = `session-${Date.now()}`;
    this.currentSession = {
      id: sessionId,
      messages: [],
      fileHandles: new Map(),
      createdAt: new Date(),
      lastActivity: new Date()
    };
    
    await this.saveSession();
    console.log(`[SessionService] Created new session: ${sessionId}`);
    return sessionId;
  }

  async loadSession(sessionId: string): Promise<boolean> {
    try {
      const sessionPath = path.join(this.sessionsDir, `${sessionId}.json`);
      const data = await fs.readFile(sessionPath, 'utf-8');
      const parsed = JSON.parse(data);
      
      this.currentSession = {
        id: parsed.id,
        messages: parsed.messages.map((m: any) => ({
          ...m,
          timestamp: new Date(m.timestamp)
        })),
        fileHandles: new Map(Object.entries(parsed.fileHandles)),
        createdAt: new Date(parsed.createdAt),
        lastActivity: new Date(parsed.lastActivity)
      };
      
      console.log(`[SessionService] Loaded session: ${sessionId} with ${this.currentSession.messages.length} messages`);
      this.emit('session-loaded', this.currentSession);
      return true;
    } catch (error) {
      console.error(`[SessionService] Failed to load session ${sessionId}:`, error);
      return false;
    }
  }

  async saveSession() {
    if (!this.currentSession) return;
    
    try {
      const sessionPath = path.join(this.sessionsDir, `${this.currentSession.id}.json`);
      const data = {
        ...this.currentSession,
        fileHandles: Object.fromEntries(this.currentSession.fileHandles),
        lastActivity: new Date()
      };
      
      await fs.writeFile(sessionPath, JSON.stringify(data, null, 2));
      console.log(`[SessionService] Saved session: ${this.currentSession.id}`);
    } catch (error) {
      console.error('[SessionService] Failed to save session:', error);
    }
  }

  async listSessions(): Promise<Array<{ id: string; createdAt: Date; messageCount: number }>> {
    try {
      const files = await fs.readdir(this.sessionsDir);
      const sessions = [];
      
      for (const file of files) {
        if (file.endsWith('.json')) {
          try {
            const data = await fs.readFile(path.join(this.sessionsDir, file), 'utf-8');
            const parsed = JSON.parse(data);
            sessions.push({
              id: parsed.id,
              createdAt: new Date(parsed.createdAt),
              messageCount: parsed.messages.length
            });
          } catch (error) {
            console.error(`[SessionService] Failed to read session ${file}:`, error);
          }
        }
      }
      
      return sessions.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    } catch (error) {
      console.error('[SessionService] Failed to list sessions:', error);
      return [];
    }
  }

  addMessage(message: ConversationMessage) {
    if (!this.currentSession) {
      this.createSession();
    }
    
    this.currentSession!.messages.push(message);
    this.currentSession!.lastActivity = new Date();
    this.saveSession(); // Auto-save
  }

  addFileHandle(handleId: string, absolutePath: string) {
    if (!this.currentSession) return;
    
    this.currentSession.fileHandles.set(handleId, absolutePath);
    this.saveSession();
  }

  getCurrentSession(): Session | null {
    return this.currentSession;
  }

  getMessages(): ConversationMessage[] {
    return this.currentSession?.messages || [];
  }

  getFileHandles(): Map<string, string> {
    return this.currentSession?.fileHandles || new Map();
  }
}

// Singleton instance
let sessionService: SessionService | null = null;

export function getSessionService(): SessionService {
  if (!sessionService) {
    sessionService = new SessionService();
  }
  return sessionService;
}