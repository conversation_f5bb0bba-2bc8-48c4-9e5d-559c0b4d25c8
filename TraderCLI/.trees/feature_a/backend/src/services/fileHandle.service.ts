import { EventEmitter } from 'events';
import * as path from 'path';
import * as fs from 'fs/promises';
import { getStorageService, StorageService } from './storage.service';

interface FileHandle {
  handleId: string;
  absolutePath: string;
  filename: string;
  size?: number;
  createdAt: Date;
  lastAccessed: Date;
  userId?: string;
  sessionId?: string;
  fileId?: string; // Storage service file ID
}

export class FileHandleManager extends EventEmitter {
  private handles: Map<string, FileHandle> = new Map();
  private handleCounter: number = 0;
  private workspaceRoot: string;
  private storageService: StorageService;
  private currentUserId: string = 'default';
  private currentSessionId: string = 'default';

  constructor(workspaceRoot: string = process.cwd()) {
    super();
    this.workspaceRoot = workspaceRoot;
    this.storageService = getStorageService();
    console.log('[FileHandleManager] Initialized with root:', workspaceRoot);
  }

  setUserContext(userId: string, sessionId: string) {
    this.currentUserId = userId;
    this.currentSessionId = sessionId;
    console.log(`[FileHandleManager] Set user context: ${userId}/${sessionId}`);
  }

  /**
   * Create a new file handle for a given path
   */
  async createHandle(filePath: string, content?: Buffer | string): Promise<FileHandle> {
    // Resolve to absolute path
    const absolutePath = path.isAbsolute(filePath) 
      ? filePath 
      : path.resolve(this.workspaceRoot, filePath);

    // Check if handle already exists for this path
    for (const [id, handle] of this.handles) {
      if (handle.absolutePath === absolutePath) {
        handle.lastAccessed = new Date();
        console.log(`[FileHandleManager] Reusing handle ${id} for ${absolutePath}`);
        return handle;
      }
    }

    // Create new handle
    this.handleCounter++;
    const handleId = `@F${this.handleCounter}`;
    
    let size: number | undefined;
    let fileId: string | undefined;
    
    // If content is provided, store it in the storage service
    if (content) {
      const filename = path.basename(absolutePath);
      const mimeType = this.getMimeType(filename);
      
      try {
        const storedFile = await this.storageService.storeFile(
          this.currentUserId,
          this.currentSessionId,
          filename,
          content,
          mimeType
        );
        
        fileId = storedFile.id;
        size = storedFile.size;
        console.log(`[FileHandleManager] Stored file in storage service: ${fileId}`);
      } catch (error) {
        console.error('[FileHandleManager] Failed to store file:', error);
        throw error;
      }
    } else {
      // Try to get file stats for existing files
      try {
        const stats = await fs.stat(absolutePath);
        size = stats.size;
      } catch (error) {
        console.log(`[FileHandleManager] File not found: ${absolutePath}`);
      }
    }

    const handle: FileHandle = {
      handleId,
      absolutePath,
      filename: path.basename(absolutePath),
      size,
      createdAt: new Date(),
      lastAccessed: new Date(),
      userId: this.currentUserId,
      sessionId: this.currentSessionId,
      fileId
    };

    this.handles.set(handleId, handle);
    console.log(`[FileHandleManager] Created handle ${handleId} for ${absolutePath}`);
    
    // Emit event for UI updates
    this.emit('handle-created', handle);
    
    return handle;
  }

  private getMimeType(filename: string): string {
    const ext = path.extname(filename).toLowerCase();
    const mimeTypes: Record<string, string> = {
      '.csv': 'text/csv',
      '.json': 'application/json',
      '.txt': 'text/plain',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.xls': 'application/vnd.ms-excel'
    };
    return mimeTypes[ext] || 'application/octet-stream';
  }

  /**
   * Resolve a handle ID to its absolute path
   */
  async resolvePath(handleId: string): Promise<string | null> {
    const handle = this.handles.get(handleId);
    if (!handle) {
      console.error(`[FileHandleManager] Handle not found: ${handleId}`);
      return null;
    }
    
    handle.lastAccessed = new Date();
    
    // If file is stored in storage service, retrieve it
    if (handle.fileId && handle.userId) {
      try {
        const { file, content } = await this.storageService.getFile(handle.userId, handle.fileId);
        
        // Create a temporary file for the agent to access
        const tempPath = path.join('/tmp', `tradercli_${handle.fileId}_${handle.filename}`);
        await fs.writeFile(tempPath, content);
        
        console.log(`[FileHandleManager] Retrieved file from storage: ${tempPath}`);
        return tempPath;
      } catch (error) {
        console.error('[FileHandleManager] Failed to retrieve file from storage:', error);
      }
    }
    
    return handle.absolutePath;
  }

  /**
   * Get a handle by ID
   */
  getHandle(handleId: string): FileHandle | null {
    const handle = this.handles.get(handleId);
    if (handle) {
      handle.lastAccessed = new Date();
    }
    return handle || null;
  }

  /**
   * Get all active handles
   */
  getAllHandles(): FileHandle[] {
    return Array.from(this.handles.values());
  }

  /**
   * Remove a handle
   */
  removeHandle(handleId: string): boolean {
    const handle = this.handles.get(handleId);
    if (handle) {
      this.handles.delete(handleId);
      console.log(`[FileHandleManager] Removed handle ${handleId}`);
      this.emit('handle-removed', handle);
      return true;
    }
    return false;
  }

  /**
   * Clear all handles
   */
  clearHandles(): void {
    const count = this.handles.size;
    this.handles.clear();
    this.handleCounter = 0;
    console.log(`[FileHandleManager] Cleared ${count} handles`);
    this.emit('handles-cleared');
  }

  /**
   * Replace handle references in a command string
   * e.g., "python3 @F1" -> "python3 '/path/to/file.py'"
   */
  async resolveCommand(command: string): Promise<string> {
    let resolvedCommand = command;
    
    // Find all handle references in the command
    const handlePattern = /@F\d+/g;
    const matches = command.match(handlePattern);
    
    if (matches) {
      for (const match of matches) {
        const absolutePath = await this.resolvePath(match);
        if (absolutePath) {
          // Properly escape the path for shell
          const escapedPath = `'${absolutePath.replace(/'/g, "'\\''")}'`;
          resolvedCommand = resolvedCommand.replace(match, escapedPath);
          console.log(`[FileHandleManager] Resolved ${match} to ${escapedPath}`);
        }
      }
    }
    
    return resolvedCommand;
  }

  /**
   * Update file size after write operations
   */
  async updateFileSize(handleId: string): Promise<void> {
    const handle = this.handles.get(handleId);
    if (handle) {
      try {
        const stats = await fs.stat(handle.absolutePath);
        handle.size = stats.size;
        this.emit('handle-updated', handle);
      } catch (error) {
        console.error(`[FileHandleManager] Failed to update size for ${handleId}:`, error);
      }
    }
  }
}

// Singleton instance
let fileHandleManager: FileHandleManager | null = null;

export function getFileHandleManager(workspaceRoot?: string): FileHandleManager {
  if (!fileHandleManager) {
    fileHandleManager = new FileHandleManager(workspaceRoot);
  }
  return fileHandleManager;
}