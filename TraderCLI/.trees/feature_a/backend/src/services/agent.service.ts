import { EventEmitter } from 'events';
import { io, Socket } from 'socket.io-client';
import { getFileHandleManager, FileHandleManager } from './fileHandle.service';

interface ProcessOptions {
  onStream?: (chunk: string) => void;
  onToolUse?: (tool: any) => void;
  onComplete?: (result: any) => void;
  onStateChange?: (state: { state: string; description: string; details?: string }) => void;
  onArtifact?: (artifact: any) => void;
}

export class AgentService extends EventEmitter {
  private agentSocket: Socket | null = null;
  private isReady: boolean = false;
  private messageQueue: Array<{ message: string; options: ProcessOptions }> = [];
  private currentOptions: ProcessOptions | null = null;
  private fileHandleManager: FileHandleManager;
  private isReadingFile = false;
  private currentFileRead: { path?: string; content: string } = { content: '' };
  private streamBuffer = '';
  private codeBlockCount = 0;
  private isRunningCommand = false;
  private currentCommand: { command?: string; output: string } = { output: '' };

  constructor() {
    super();
    console.log('[AgentService] Initializing agent service...');
    console.log('[AgentService] GEMINI_API_KEY present:', !!process.env.GEMINI_API_KEY);
    
    // Initialize file handle manager
    this.fileHandleManager = getFileHandleManager();
    
    // Connect to the agentcli WebSocket server
    // Note: agentcli should be started separately using start-agentcli.sh
    this.connectToAgentSocket();
  }


  private connectToAgentSocket() {
    console.log('[AgentService] Connecting to agentcli WebSocket server...');
    console.log('[AgentService] Connection URL: http://localhost:3001');
    
    this.agentSocket = io('http://localhost:3001', {
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      transports: ['websocket', 'polling'],
    });

    this.agentSocket.on('connect', () => {
      console.log('[AgentService] Connected to agentcli WebSocket server');
      console.log('[AgentService] Socket ID:', this.agentSocket?.id);
      this.isReady = true;
      
      // Process any queued messages
      console.log('[AgentService] Message queue length:', this.messageQueue.length);
      if (this.messageQueue.length > 0) {
        const { message, options } = this.messageQueue.shift()!;
        console.log('[AgentService] Processing queued message:', message);
        this.sendToAgent(message, options);
      }
    });

    this.agentSocket.on('ready', (data) => {
      console.log('[AgentService] AgentCLI ready event received:', data);
      console.log('[AgentService] Model:', data.model);
      console.log('[AgentService] Recovered:', data.recovered);
      this.isReady = true;
    });

    this.agentSocket.on('response', (data) => {
      console.log(`[AgentService] Response received, type: ${data.type}`);
      
      if (!this.currentOptions) {
        console.warn('[AgentService] Received response but no currentOptions set');
        return;
      }

      switch (data.type) {
        case 'stream':
          console.log(`[AgentService] Stream chunk:`, data.content?.substring(0, 50) + '...');
          
          // If we're reading a file, capture the content and emit as artifact
          if (this.isReadingFile && data.content) {
            this.currentFileRead.content += data.content;
            
            // Emit streaming file artifact
            if (this.currentOptions.onArtifact && this.currentFileRead.path) {
              const pathParts = this.currentFileRead.path.split('.');
              const extension = pathParts[pathParts.length - 1];
              const filename = this.currentFileRead.path.split('/').pop() || 'file';
              const languageMap: Record<string, string> = {
                'js': 'javascript', 'ts': 'typescript', 'tsx': 'typescript',
                'jsx': 'javascript', 'json': 'json', 'py': 'python',
                'rs': 'rust', 'go': 'go', 'java': 'java',
                'cpp': 'cpp', 'c': 'c', 'h': 'c', 'hpp': 'cpp',
                'cs': 'csharp', 'rb': 'ruby', 'php': 'php',
                'swift': 'swift', 'kt': 'kotlin', 'scala': 'scala',
                'r': 'r', 'sql': 'sql', 'sh': 'bash',
                'bash': 'bash', 'zsh': 'bash', 'fish': 'bash',
                'yml': 'yaml', 'yaml': 'yaml', 'xml': 'xml',
                'html': 'html', 'css': 'css', 'scss': 'scss',
                'sass': 'sass', 'less': 'less', 'md': 'markdown',
                'dockerfile': 'dockerfile', 'makefile': 'makefile'
              };
              
              this.currentOptions.onArtifact({
                id: `file-read-${this.currentFileRead.path.replace(/[^a-zA-Z0-9]/g, '-')}`,
                type: 'code',
                title: filename,
                content: this.currentFileRead.content,
                language: languageMap[extension.toLowerCase()] || 'plaintext',
                path: this.currentFileRead.path,
                isStreaming: true
              });
            }
            
            // Don't send file content to main stream
            return;
          }
          
          // If we're running a command, capture the output and emit as artifact
          if (this.isRunningCommand && data.content) {
            this.currentCommand.output += data.content;
            
            // Emit streaming command output artifact
            if (this.currentOptions.onArtifact && this.currentCommand.command) {
              this.currentOptions.onArtifact({
                id: `command-${this.currentCommand.command.replace(/[^a-zA-Z0-9]/g, '-')}`,
                type: 'terminal',
                title: this.currentCommand.command,
                content: this.currentCommand.output,
                isStreaming: true
              });
            }
            
            // Don't send command output to main stream
            return;
          }
          
          
          // Check for code blocks in the stream
          if (data.content && this.currentOptions.onArtifact) {
            this.streamBuffer += data.content;
            
            // Debug: Check if we have potential code blocks
            if (this.streamBuffer.includes('```')) {
              console.log('[AgentService] Potential code block detected in stream');
            }
            
            this.extractCodeBlocks();
          }
          
          // Only send to stream if not reading a file
          if (this.currentOptions.onStream && !this.isReadingFile) {
            this.currentOptions.onStream(data.content);
          }
          break;
        case 'tool':
          console.log('[AgentService] Tool use:', JSON.stringify(data, null, 2));
          
          // Extract tool name from the correct structure
          const toolName = data.tool?.name || data.name || 'unknown';
          console.log('[AgentService] Extracted tool name:', toolName);
          
          // Also check for alternative tool structures
          const toolArgs = data.tool?.args || data.args || {};
          const toolResult = data.tool?.result || data.result;
          
          // Debug: Check if result is present
          console.log('[AgentService] Tool has result?', toolResult !== undefined);
          if (toolResult !== undefined) {
            console.log('[AgentService] Tool result type:', typeof toolResult);
            console.log('[AgentService] Tool result keys:', toolResult && typeof toolResult === 'object' ? Object.keys(toolResult) : 'N/A');
          }
          
          // Track file reads
          if (toolName === 'read_file') {
            this.isReadingFile = true;
            this.currentFileRead = {
              path: data.tool?.args?.absolute_path || data.tool?.args?.path || data.tool?.args?.file_path,
              content: ''
            };
            console.log('[AgentService] Starting to read file:', this.currentFileRead.path);
          }
          
          // Capture write_file content as artifact
          if (toolName === 'write_file' && data.tool?.args?.content) {
            const filePath = data.tool?.args?.absolute_path || data.tool?.args?.path || data.tool?.args?.file_path || 'untitled';
            const content = data.tool?.args?.content;
            const pathParts = filePath.split('.');
            const extension = pathParts[pathParts.length - 1];
            const filename = filePath.split('/').pop() || 'untitled';
            
            const languageMap: Record<string, string> = {
              'py': 'python', 'js': 'javascript', 'ts': 'typescript',
              'jsx': 'javascript', 'tsx': 'typescript', 'json': 'json',
              'rs': 'rust', 'go': 'go', 'java': 'java', 'cpp': 'cpp',
              'c': 'c', 'rb': 'ruby', 'php': 'php', 'swift': 'swift',
              'kt': 'kotlin', 'scala': 'scala', 'r': 'r', 'sql': 'sql',
              'sh': 'bash', 'yml': 'yaml', 'yaml': 'yaml', 'xml': 'xml',
              'html': 'html', 'css': 'css', 'md': 'markdown'
            };
            
            if (this.currentOptions.onArtifact) {
              this.currentOptions.onArtifact({
                id: `write-file-${Date.now()}`,
                type: 'code',
                title: filename,
                content: content,
                language: languageMap[extension] || 'plaintext',
                path: filePath
              });
            }
          }
          
          // Track shell commands
          if (toolName === 'run_shell_command' || toolName === 'execute_command') {
            this.isRunningCommand = true;
            this.currentCommand = {
              command: data.tool?.args?.command || 'unknown command',
              output: ''
            };
            console.log('[AgentService] Starting to run command:', this.currentCommand.command);
          }
          // Map agentcli tool names to visual states
          const toolStates: Record<string, { state: string; description: string }> = {
            // File operations
            'read_file': { state: 'reading_files', description: 'Reading file' },
            'write_file': { state: 'writing_code', description: 'Writing file' },
            'replace': { state: 'writing_code', description: 'Editing file' },
            'list_directory': { state: 'reading_files', description: 'Listing directory' },
            'read_many_files': { state: 'reading_files', description: 'Reading multiple files' },
            
            // Search operations
            'search_file_content': { state: 'searching', description: 'Searching files' },
            'glob': { state: 'searching', description: 'Finding files' },
            'google_web_search': { state: 'searching', description: 'Searching the web' },
            
            // Command execution
            'run_shell_command': { state: 'running_command', description: 'Running command' },
            
            // Web operations
            'web_fetch': { state: 'searching', description: 'Fetching web content' },
            
            // Memory operations
            'save_memory': { state: 'thinking', description: 'Saving to memory' },
            
            // Data analysis (for future tools)
            'analyze_data': { state: 'analyzing_data', description: 'Analyzing data' }
          };
          
          const stateInfo = toolStates[toolName] || { state: 'thinking', description: `Using ${toolName}` };
          console.log(`[AgentService] Tool ${toolName} mapped to state:`, stateInfo);
          if (this.currentOptions.onStateChange) {
            console.log('[AgentService] Calling onStateChange callback');
            this.currentOptions.onStateChange({
              ...stateInfo,
              details: data.tool?.args?.command || data.tool?.args?.path || data.tool?.args?.query || data.tool?.args?.file_path
            });
          } else {
            console.warn('[AgentService] No onStateChange callback available!');
          }
          
          // Intercept tool calls to handle file operations
          this.processTool(data.tool);
          if (this.currentOptions.onToolUse) {
            // Pass the tool data in the format expected by index.ts
            this.currentOptions.onToolUse({
              name: data.tool?.name,
              args: data.tool?.args,
              result: data.tool?.result,
              // Mark read_file for special handling
              isFileRead: data.tool?.name === 'read_file'
            });
          }
          
          // Handle command output if it comes with the tool event
          if ((toolName === 'run_shell_command' || toolName === 'execute_command') && toolResult !== undefined) {
            console.log('[AgentService] Command result received:', toolResult);
            if (this.currentOptions.onArtifact) {
              const commandStr = toolArgs.command || data.tool?.args?.command || 'Command';
              
              // Extract the actual output from the result
              let outputContent = '';
              if (typeof toolResult === 'object' && toolResult !== null) {
                // AgentCLI returns {llmContent, returnDisplay}
                outputContent = toolResult.returnDisplay || toolResult.output || toolResult.stdout || '';
                
                // If returnDisplay is empty, try to extract from llmContent
                if (!outputContent && toolResult.llmContent) {
                  const stdoutMatch = toolResult.llmContent.match(/Stdout:\s*(.+?)(?:\n|$)/);
                  if (stdoutMatch) {
                    outputContent = stdoutMatch[1] === '(empty)' ? '' : stdoutMatch[1];
                  }
                }
              } else if (typeof toolResult === 'string') {
                outputContent = toolResult;
              }
              
              console.log('[AgentService] Extracted command output:', outputContent);
              
              this.currentOptions.onArtifact({
                id: `command-${Date.now()}`,
                type: 'terminal',
                title: commandStr,
                content: outputContent || '(No output)',
                isStreaming: false
              });
            }
          }
          break;
        case 'complete':
          console.log('[AgentService] Response complete');
          
          // Reset file reading state (artifact already emitted during streaming)
          if (this.isReadingFile) {
            console.log('[AgentService] File read complete:', this.currentFileRead.path);
            this.isReadingFile = false;
            this.currentFileRead = { content: '' };
          }
          
          // Reset command running state
          if (this.isRunningCommand) {
            console.log('[AgentService] Command complete:', this.currentCommand.command);
            this.isRunningCommand = false;
            this.currentCommand = { output: '' };
          }
          
          // Reset stream buffer and code block counter
          this.streamBuffer = '';
          this.codeBlockCount = 0;
          
          // Return to idle state
          if (this.currentOptions.onStateChange) {
            this.currentOptions.onStateChange({
              state: 'idle',
              description: 'Agent Ready'
            });
          }
          
          if (this.currentOptions.onComplete) {
            this.currentOptions.onComplete({ result: 'completed' });
          }
          this.currentOptions = null;
          
          // Process next queued message
          console.log('[AgentService] Queue length after completion:', this.messageQueue.length);
          if (this.messageQueue.length > 0) {
            const { message, options } = this.messageQueue.shift()!;
            console.log('[AgentService] Processing next queued message:', message);
            this.sendToAgent(message, options);
          }
          break;
        case 'tool_response':
        case 'toolResponse':
          // Handle tool response events (some versions of agentcli use this)
          console.log('[AgentService] Tool response received:', data);
          const respToolName = data.tool?.name || data.name;
          const respResult = data.result || data.output || data.tool?.result;
          
          if ((respToolName === 'run_shell_command' || respToolName === 'execute_command') && respResult) {
            console.log('[AgentService] Command response:', respResult);
            if (this.currentOptions.onArtifact) {
              this.currentOptions.onArtifact({
                id: `command-response-${Date.now()}`,
                type: 'terminal',
                title: data.tool?.args?.command || data.command || 'Command Output',
                content: typeof respResult === 'string' ? respResult : JSON.stringify(respResult, null, 2),
                isStreaming: false
              });
            }
          }
          break;
        case 'error':
          console.error('[AgentService] ERROR from agentcli:', data.error);
          if (this.currentOptions.onStream) {
            this.currentOptions.onStream(`Error: ${data.error}\n`);
          }
          if (this.currentOptions.onComplete) {
            this.currentOptions.onComplete({ error: data.error });
          }
          this.currentOptions = null;
          break;
        default:
          console.warn(`[AgentService] Unknown response type: ${data.type}`);
      }
    });

    this.agentSocket.on('error', (error) => {
      console.error('[AgentService] Socket error:', error);
      console.error('[AgentService] Error details:', {
        message: (error as any).message || 'No message',
        type: (error as any).type || 'No type',
        stack: (error as any).stack || 'No stack trace'
      });
      if (this.currentOptions?.onStream) {
        this.currentOptions.onStream(`Connection error: ${(error as any).message}\n`);
      }
    });
    
    this.agentSocket.on('connect_error', (error) => {
      console.error('[AgentService] Connection error:', (error as any).message);
      console.error('[AgentService] Make sure agentcli WebSocket server is running on port 3001');
    });

    this.agentSocket.on('disconnect', (reason) => {
      console.log('[AgentService] Disconnected from agentcli:', reason);
      this.isReady = false;
      console.log('[AgentService] Will attempt to reconnect...');
    });
    
    // Listen for any other events that might contain tool results
    this.agentSocket.onAny((eventName, data) => {
      if (eventName !== 'response' && eventName !== 'ready' && eventName !== 'connect') {
        console.log(`[AgentService] Received event '${eventName}':`, JSON.stringify(data, null, 2));
      }
    });
  }

  async processMessage(message: string, options: ProcessOptions) {
    console.log('[AgentService] processMessage called with:', message);
    console.log('[AgentService] Socket connected:', !!this.agentSocket?.connected);
    console.log('[AgentService] Agent ready:', this.isReady);
    console.log('[AgentService] Current options set:', !!this.currentOptions);

    if (!this.agentSocket || !this.agentSocket.connected) {
      console.log('[AgentService] Socket not connected, queueing message');
      options.onStream?.('⚠️ Agent not connected. Please wait...\n');
      this.messageQueue.push({ message, options });
      return;
    }

    if (!this.isReady || this.currentOptions) {
      console.log('[AgentService] Agent busy or not ready, queueing message');
      console.log('[AgentService] Current queue size:', this.messageQueue.length);
      this.messageQueue.push({ message, options });
      return;
    }

    // Emit thinking state
    options.onStateChange?.({
      state: 'thinking',
      description: 'Processing your request',
      details: message.substring(0, 100) + (message.length > 100 ? '...' : '')
    });

    this.sendToAgent(message, options);
  }

  private async sendToAgent(message: string, options: ProcessOptions) {
    console.log('[AgentService] sendToAgent called');
    console.log('[AgentService] Socket connected:', this.agentSocket?.connected);
    console.log('[AgentService] Socket ID:', this.agentSocket?.id);
    
    if (!this.agentSocket || !this.agentSocket.connected) {
      console.error('[AgentService] ERROR: Cannot send to agent - not connected');
      options.onStream?.('❌ Socket not connected to agentcli\n');
      return;
    }

    this.currentOptions = options;
    
    // Resolve file handles in the message before sending
    const resolvedMessage = await this.fileHandleManager.resolveCommand(message);
    console.log('[AgentService] Original message:', message);
    console.log('[AgentService] Resolved message:', resolvedMessage);
    
    // Send the resolved message to agentcli
    this.agentSocket.emit('message', { message: resolvedMessage });
    console.log('[AgentService] Message sent successfully');
  }

  private async processTool(tool: any) {
    if (!tool || !tool.name) return;

    // Handle file operations to create/update handles
    switch (tool.name) {
      case 'write_file':
      case 'create_file':
        if (tool.args?.path || tool.args?.file_path) {
          const filePath = tool.args.path || tool.args.file_path;
          const handle = await this.fileHandleManager.createHandle(filePath);
          console.log(`[AgentService] Created handle ${handle.handleId} for write_file`);
          
          // Update tool result to include handle
          tool.result = {
            ...tool.result,
            handle: {
              handleId: handle.handleId,
              absolutePath: handle.absolutePath,
              filename: handle.filename
            }
          };
        }
        break;

      case 'read_file':
        if (tool.args?.path || tool.args?.file_path) {
          const filePath = tool.args.path || tool.args.file_path;
          const handle = await this.fileHandleManager.createHandle(filePath);
          console.log(`[AgentService] Created handle ${handle.handleId} for read_file`);
        }
        break;

      case 'run_shell_command':
      case 'execute_command':
        if (tool.args?.command) {
          // Resolve any file handles in the command
          const resolvedCommand = await this.fileHandleManager.resolveCommand(tool.args.command);
          if (resolvedCommand !== tool.args.command) {
            console.log(`[AgentService] Resolved command: ${resolvedCommand}`);
            // Update the tool args with resolved command
            tool.args.command = resolvedCommand;
          }
        }
        break;
    }
  }

  getFileHandles() {
    return this.fileHandleManager.getAllHandles();
  }

  destroy() {
    if (this.agentSocket) {
      this.agentSocket.disconnect();
      this.agentSocket = null;
    }
  }

  private extractCodeBlocks() {
    // Regular expression to match code blocks with optional language
    // Handle various markdown code block formats
    const codeBlockRegex = /```(\w*)\s*\n([\s\S]*?)```/g;
    const incompleteCodeBlockRegex = /```(\w*)\s*\n([\s\S]*)$/;
    let match;
    let lastIndex = 0;
    
    // First, check for complete code blocks
    while ((match = codeBlockRegex.exec(this.streamBuffer)) !== null) {
      const language = match[1] || 'plaintext';
      const code = match[2].trim();
      
      // Check if this is a new code block (not already emitted)
      if (match.index >= lastIndex) {
        this.codeBlockCount++;
        const blockId = `code-block-${this.codeBlockCount}`;
        
        console.log(`[AgentService] Extracted code block ${this.codeBlockCount}:`, {
          language: language || 'plaintext',
          contentLength: code.length,
          firstLine: code.split('\n')[0]
        });
        
        // Emit the code block as an artifact
        if (this.currentOptions?.onArtifact) {
          this.currentOptions.onArtifact({
            id: blockId,
            type: 'code',
            title: language === 'plaintext' ? `Code Block ${this.codeBlockCount}` : `${language} code`,
            content: code,
            language: language || 'plaintext',
            isFromStream: true
          });
        }
        
        lastIndex = match.index + match[0].length;
      }
    }
    
    // Check for incomplete code blocks (still streaming)
    const incompleteMatch = incompleteCodeBlockRegex.exec(this.streamBuffer.substring(lastIndex));
    if (incompleteMatch) {
      const language = incompleteMatch[1] || 'plaintext';
      const partialCode = incompleteMatch[2];
      const blockId = `code-block-streaming-${this.codeBlockCount + 1}`;
      
      // Emit partial code block
      if (this.currentOptions?.onArtifact && partialCode.length > 0) {
        this.currentOptions.onArtifact({
          id: blockId,
          type: 'code',
          title: language === 'plaintext' ? `Code Block (streaming...)` : `${language} code (streaming...)`,
          content: partialCode,
          language: language,
          isStreaming: true
        });
      }
    }
    
    // Keep only the unprocessed part of the buffer
    if (lastIndex > 0) {
      this.streamBuffer = this.streamBuffer.substring(lastIndex);
    }
    
    // Prevent buffer from growing too large
    if (this.streamBuffer.length > 10000) {
      this.streamBuffer = this.streamBuffer.substring(this.streamBuffer.length - 5000);
    }
  }
}