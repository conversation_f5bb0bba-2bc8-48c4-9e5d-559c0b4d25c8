import * as path from 'path';
import * as fs from 'fs/promises';
import * as crypto from 'crypto';
import { EventEmitter } from 'events';

interface UserFile {
  id: string;
  userId: string;
  sessionId: string;
  originalName: string;
  storagePath: string;
  size: number;
  mimeType: string;
  uploadedAt: Date;
  lastAccessed: Date;
  metadata?: Record<string, any>;
}

interface StorageOptions {
  baseDir?: string;
  maxFileSize?: number; // bytes
  maxFilesPerUser?: number;
  allowedMimeTypes?: string[];
}

export class StorageService extends EventEmitter {
  private baseDir: string;
  private maxFileSize: number;
  private maxFilesPerUser: number;
  private allowedMimeTypes: Set<string>;
  
  constructor(options: StorageOptions = {}) {
    super();
    
    // Use environment variable or default to user's home directory
    this.baseDir = options.baseDir || process.env.TRADERCLI_STORAGE_DIR || 
      path.join(process.env.HOME || '/tmp', '.tradercli', 'uploads');
    
    this.maxFileSize = options.maxFileSize || 50 * 1024 * 1024; // 50MB default
    this.maxFilesPerUser = options.maxFilesPerUser || 100;
    this.allowedMimeTypes = new Set(options.allowedMimeTypes || [
      'text/csv',
      'application/json',
      'text/plain',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]);
    
    this.initializeStorage();
  }
  
  private async initializeStorage() {
    try {
      await fs.mkdir(this.baseDir, { recursive: true });
      console.log(`[StorageService] Initialized storage at: ${this.baseDir}`);
    } catch (error) {
      console.error('[StorageService] Failed to initialize storage:', error);
      throw error;
    }
  }
  
  /**
   * Store a file for a specific user and session
   */
  async storeFile(
    userId: string, 
    sessionId: string, 
    filename: string, 
    content: Buffer | string,
    mimeType?: string
  ): Promise<UserFile> {
    // Validate file size
    const size = Buffer.byteLength(content);
    if (size > this.maxFileSize) {
      throw new Error(`File size ${size} exceeds maximum allowed size of ${this.maxFileSize}`);
    }
    
    // Validate mime type if provided
    if (mimeType && !this.allowedMimeTypes.has(mimeType)) {
      throw new Error(`File type ${mimeType} is not allowed`);
    }
    
    // Create user and session directories
    const userDir = path.join(this.baseDir, this.sanitizeId(userId));
    const sessionDir = path.join(userDir, this.sanitizeId(sessionId));
    await fs.mkdir(sessionDir, { recursive: true });
    
    // Check user's file count
    const existingFiles = await this.getUserFileCount(userId);
    if (existingFiles >= this.maxFilesPerUser) {
      throw new Error(`User has reached maximum file limit of ${this.maxFilesPerUser}`);
    }
    
    // Generate unique file ID and storage path
    const fileId = this.generateFileId();
    const safeFilename = this.sanitizeFilename(filename);
    const storagePath = path.join(sessionDir, `${fileId}_${safeFilename}`);
    
    // Write file to disk
    await fs.writeFile(storagePath, content);
    
    // Create file metadata
    const userFile: UserFile = {
      id: fileId,
      userId,
      sessionId,
      originalName: filename,
      storagePath,
      size,
      mimeType: mimeType || 'application/octet-stream',
      uploadedAt: new Date(),
      lastAccessed: new Date()
    };
    
    // Store metadata
    const metadataPath = storagePath + '.meta.json';
    await fs.writeFile(metadataPath, JSON.stringify(userFile, null, 2));
    
    console.log(`[StorageService] Stored file ${fileId} for user ${userId}`);
    this.emit('file-stored', userFile);
    
    return userFile;
  }
  
  /**
   * Retrieve a file by ID
   */
  async getFile(userId: string, fileId: string): Promise<{ file: UserFile; content: Buffer }> {
    const userDir = path.join(this.baseDir, this.sanitizeId(userId));
    
    // Search for the file in all user's sessions
    const sessions = await fs.readdir(userDir);
    
    for (const session of sessions) {
      const sessionDir = path.join(userDir, session);
      const files = await fs.readdir(sessionDir);
      
      const fileEntry = files.find(f => f.startsWith(fileId + '_') && !f.endsWith('.meta.json'));
      if (fileEntry) {
        const storagePath = path.join(sessionDir, fileEntry);
        const metadataPath = storagePath + '.meta.json';
        
        // Read metadata
        const metadata = JSON.parse(await fs.readFile(metadataPath, 'utf-8')) as UserFile;
        
        // Update last accessed time
        metadata.lastAccessed = new Date();
        await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2));
        
        // Read file content
        const content = await fs.readFile(storagePath);
        
        return { file: metadata, content };
      }
    }
    
    throw new Error(`File ${fileId} not found for user ${userId}`);
  }
  
  /**
   * Get file by absolute path (for backward compatibility)
   */
  async getFileByPath(absolutePath: string): Promise<{ file: UserFile; content: Buffer }> {
    // Extract file ID from path
    const filename = path.basename(absolutePath);
    const match = filename.match(/^([a-f0-9]+)_/);
    if (!match) {
      throw new Error('Invalid file path format');
    }
    
    const fileId = match[1];
    
    // Read metadata
    const metadataPath = absolutePath + '.meta.json';
    const metadata = JSON.parse(await fs.readFile(metadataPath, 'utf-8')) as UserFile;
    
    // Read content
    const content = await fs.readFile(absolutePath);
    
    return { file: metadata, content };
  }
  
  /**
   * List all files for a user
   */
  async listUserFiles(userId: string): Promise<UserFile[]> {
    const userDir = path.join(this.baseDir, this.sanitizeId(userId));
    const files: UserFile[] = [];
    
    try {
      const sessions = await fs.readdir(userDir);
      
      for (const session of sessions) {
        const sessionDir = path.join(userDir, session);
        const sessionFiles = await fs.readdir(sessionDir);
        
        for (const file of sessionFiles) {
          if (file.endsWith('.meta.json')) {
            const metadataPath = path.join(sessionDir, file);
            const metadata = JSON.parse(await fs.readFile(metadataPath, 'utf-8')) as UserFile;
            files.push(metadata);
          }
        }
      }
    } catch (error) {
      // User directory doesn't exist yet
      console.log(`[StorageService] No files found for user ${userId}`);
    }
    
    return files;
  }
  
  /**
   * Delete a file
   */
  async deleteFile(userId: string, fileId: string): Promise<void> {
    const { file } = await this.getFile(userId, fileId);
    
    // Delete file and metadata
    await fs.unlink(file.storagePath);
    await fs.unlink(file.storagePath + '.meta.json');
    
    console.log(`[StorageService] Deleted file ${fileId} for user ${userId}`);
    this.emit('file-deleted', file);
  }
  
  /**
   * Clean up old files (e.g., files not accessed in 30 days)
   */
  async cleanupOldFiles(daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    
    let deletedCount = 0;
    
    const users = await fs.readdir(this.baseDir);
    
    for (const user of users) {
      const userDir = path.join(this.baseDir, user);
      const sessions = await fs.readdir(userDir);
      
      for (const session of sessions) {
        const sessionDir = path.join(userDir, session);
        const files = await fs.readdir(sessionDir);
        
        for (const file of files) {
          if (file.endsWith('.meta.json')) {
            const metadataPath = path.join(sessionDir, file);
            const metadata = JSON.parse(await fs.readFile(metadataPath, 'utf-8')) as UserFile;
            
            if (new Date(metadata.lastAccessed) < cutoffDate) {
              await this.deleteFile(metadata.userId, metadata.id);
              deletedCount++;
            }
          }
        }
      }
    }
    
    console.log(`[StorageService] Cleaned up ${deletedCount} old files`);
    return deletedCount;
  }
  
  /**
   * Get storage statistics
   */
  async getStorageStats(): Promise<{
    totalUsers: number;
    totalFiles: number;
    totalSize: number;
    averageFileSize: number;
  }> {
    let totalFiles = 0;
    let totalSize = 0;
    
    const users = await fs.readdir(this.baseDir);
    
    for (const user of users) {
      const userFiles = await this.listUserFiles(user);
      totalFiles += userFiles.length;
      totalSize += userFiles.reduce((sum, file) => sum + file.size, 0);
    }
    
    return {
      totalUsers: users.length,
      totalFiles,
      totalSize,
      averageFileSize: totalFiles > 0 ? totalSize / totalFiles : 0
    };
  }
  
  private generateFileId(): string {
    return crypto.randomBytes(8).toString('hex');
  }
  
  private sanitizeId(id: string): string {
    return id.replace(/[^a-zA-Z0-9-_]/g, '_');
  }
  
  private sanitizeFilename(filename: string): string {
    return filename.replace(/[^a-zA-Z0-9.-]/g, '_').substring(0, 255);
  }
  
  private async getUserFileCount(userId: string): Promise<number> {
    const files = await this.listUserFiles(userId);
    return files.length;
  }
}

// Singleton instance
let storageService: StorageService | null = null;

export function getStorageService(options?: StorageOptions): StorageService {
  if (!storageService) {
    storageService = new StorageService(options);
  }
  return storageService;
}