import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { getFileHandleManager } from '../services/fileHandle.service';
import { getStorageService } from '../services/storage.service';
import * as fs from 'fs/promises';
import * as path from 'path';

describe('File Access System', () => {
  const fileHandleManager = getFileHandleManager();
  const storageService = getStorageService();
  const testUserId = 'test-user-123';
  const testSessionId = 'test-session-456';
  
  beforeAll(async () => {
    // Set up test context
    fileHandleManager.setUserContext(testUserId, testSessionId);
  });
  
  afterAll(async () => {
    // Clean up test files
    const files = await storageService.listUserFiles(testUserId);
    for (const file of files) {
      await storageService.deleteFile(testUserId, file.id);
    }
  });
  
  it('should handle CSV file upload and provide accessible path to agent', async () => {
    // Simulate CSV content
    const csvContent = `Date,Symbol,Type,Volume,Price,Profit
2024-11-11,NAS100USD,BUY,0.1,19500.00,125.50
2024-11-11,NAS100USD,SELL,0.1,19525.50,125.50`;
    
    const filename = 'test_trades.csv';
    
    // Create handle with content (simulating upload)
    const handle = await fileHandleManager.createHandle(filename, Buffer.from(csvContent));
    
    expect(handle.handleId).toMatch(/^@F\d+$/);
    expect(handle.filename).toBe(filename);
    expect(handle.fileId).toBeDefined();
    
    // Test path resolution
    const resolvedPath = await fileHandleManager.resolvePath(handle.handleId);
    expect(resolvedPath).toBeDefined();
    
    // Verify file exists at resolved path
    const fileExists = await fs.access(resolvedPath!)
      .then(() => true)
      .catch(() => false);
    expect(fileExists).toBe(true);
    
    // Read file content to verify
    const readContent = await fs.readFile(resolvedPath!, 'utf-8');
    expect(readContent).toBe(csvContent);
  });
  
  it('should resolve file handles in commands', async () => {
    // Create a test file
    const content = 'test data';
    const handle = await fileHandleManager.createHandle('data.txt', Buffer.from(content));
    
    // Test command resolution
    const command = `python3 analyze.py @F${handle.handleId.slice(2)}`;
    const resolvedCommand = await fileHandleManager.resolveCommand(command);
    
    expect(resolvedCommand).toContain('python3 analyze.py');
    expect(resolvedCommand).toContain('/tmp/tradercli_');
    expect(resolvedCommand).not.toContain('@F');
  });
  
  it('should handle multiple users uploading files simultaneously', async () => {
    const user1 = 'user-1';
    const user2 = 'user-2';
    const session1 = 'session-1';
    const session2 = 'session-2';
    
    // User 1 uploads a file
    fileHandleManager.setUserContext(user1, session1);
    const handle1 = await fileHandleManager.createHandle('trades1.csv', Buffer.from('user1 data'));
    
    // User 2 uploads a file
    fileHandleManager.setUserContext(user2, session2);
    const handle2 = await fileHandleManager.createHandle('trades2.csv', Buffer.from('user2 data'));
    
    // Verify files are isolated
    const user1Files = await storageService.listUserFiles(user1);
    const user2Files = await storageService.listUserFiles(user2);
    
    expect(user1Files.length).toBe(1);
    expect(user2Files.length).toBe(1);
    expect(user1Files[0].originalName).toBe('trades1.csv');
    expect(user2Files[0].originalName).toBe('trades2.csv');
    
    // Clean up
    await storageService.deleteFile(user1, user1Files[0].id);
    await storageService.deleteFile(user2, user2Files[0].id);
  });
  
  it('should enforce file size limits', async () => {
    const largeContent = Buffer.alloc(100 * 1024 * 1024); // 100MB
    
    await expect(
      fileHandleManager.createHandle('large.csv', largeContent)
    ).rejects.toThrow('exceeds maximum allowed size');
  });
  
  it('should validate file types', async () => {
    const executableContent = Buffer.from('#!/bin/bash\nrm -rf /');
    
    await expect(
      storageService.storeFile(testUserId, testSessionId, 'malicious.sh', executableContent, 'application/x-sh')
    ).rejects.toThrow('not allowed');
  });
});

describe('Real Trading Data Import', () => {
  const fileHandleManager = getFileHandleManager();
  const testUserId = 'trader-test';
  const testSessionId = 'import-test';
  
  beforeAll(() => {
    fileHandleManager.setUserContext(testUserId, testSessionId);
  });
  
  it('should parse OANDA CSV format correctly', async () => {
    // Real OANDA CSV format
    const oandaCsv = `Transaction ID,Date/Time,Type,Instrument,Units,Price,Interest,P/L,Balance
123456,2024-11-11 14:30:00,MARKET_ORDER_CREATE,NAS100_USD,100,19500.00,0.00,0.00,10000.00
123457,2024-11-11 14:35:00,ORDER_FILL,NAS100_USD,100,19525.50,0.00,125.50,10125.50`;
    
    const handle = await fileHandleManager.createHandle('oanda_trades.csv', Buffer.from(oandaCsv));
    const resolvedPath = await fileHandleManager.resolvePath(handle.handleId);
    
    // Verify the file can be read and parsed
    const content = await fs.readFile(resolvedPath!, 'utf-8');
    const lines = content.split('\n');
    expect(lines.length).toBe(3); // Header + 2 trades
    expect(lines[0]).toContain('Transaction ID');
  });
});