import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import dotenv from 'dotenv';
import { AgentService } from './services/agent.service.js';
import { getSessionService } from './services/session.service.js';
import { getStorageService } from './services/storage.service.js';
import { getFileHandleManager } from './services/fileHandle.service.js';

dotenv.config();

const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: ["http://localhost:3000", "http://localhost:3001", "http://127.0.0.1:3000", "http://127.0.0.1:3001"],
    credentials: true,
    methods: ["GET", "POST"]
  }
});

app.use(cors({
  origin: ["http://localhost:3000", "http://localhost:3001", "http://127.0.0.1:3000", "http://127.0.0.1:3001"],
  credentials: true
}));
app.use(express.json({ limit: '50mb' }));

// Initialize services
const agentService = new AgentService();
const sessionService = getSessionService();
const storageService = getStorageService();
const fileHandleManager = getFileHandleManager();

// Health check
app.get('/health', (_, res) => {
  res.json({ status: 'ok', agent: 'connected' });
});

// API endpoints for session management
app.get('/api/sessions', async (_, res) => {
  const sessions = await sessionService.listSessions();
  res.json(sessions);
});

app.post('/api/sessions/:sessionId/load', async (req, res) => {
  const { sessionId } = req.params;
  const loaded = await sessionService.loadSession(sessionId);
  if (loaded) {
    const session = sessionService.getCurrentSession();
    res.json({ success: true, session });
  } else {
    res.status(404).json({ success: false, error: 'Session not found' });
  }
});

// API endpoints for file management
app.get('/api/files/:userId', async (req, res) => {
  const { userId } = req.params;
  try {
    const files = await storageService.listUserFiles(userId);
    res.json(files);
  } catch (error) {
    res.status(500).json({ error: 'Failed to list files' });
  }
});

app.get('/api/files/:userId/:fileId', async (req, res) => {
  const { userId, fileId } = req.params;
  try {
    const { file, content } = await storageService.getFile(userId, fileId);
    res.json({ 
      file,
      content: content.toString('base64'),
      contentType: file.mimeType
    });
  } catch (error) {
    res.status(404).json({ error: 'File not found' });
  }
});

app.delete('/api/files/:userId/:fileId', async (req, res) => {
  const { userId, fileId } = req.params;
  try {
    await storageService.deleteFile(userId, fileId);
    res.json({ success: true });
  } catch (error) {
    res.status(404).json({ error: 'File not found' });
  }
});

app.get('/api/storage/stats', async (_, res) => {
  try {
    const stats = await storageService.getStorageStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: 'Failed to get storage stats' });
  }
});

// WebSocket connection for terminal interface
io.on('connection', async (socket) => {
  console.log('Client connected:', socket.id);
  
  // Create or resume session
  const sessionId = await sessionService.createSession();
  socket.emit('session-started', { sessionId });
  
  // Set user context for file handling
  // In production, get userId from authentication
  const userId = socket.id; // Using socket ID as temporary user ID
  fileHandleManager.setUserContext(userId, sessionId);

  socket.on('message', async (data) => {
    const { content } = data;
    console.log('Received message:', content);
    
    // Save user message to session
    sessionService.addMessage({
      role: 'user',
      content,
      timestamp: new Date()
    });

    try {
      // Process message through agent
      await agentService.processMessage(content, {
        onStream: (chunk: string) => {
          socket.emit('stream', chunk);
          // Save assistant response to session
          sessionService.addMessage({
            role: 'assistant',
            content: chunk,
            timestamp: new Date()
          });
        },
        onToolUse: (tool: any) => {
          console.log('[Backend] Tool use event:', { name: tool.name, hasResult: tool.result !== undefined });
          
          // Emit tool_call event when tool is invoked
          socket.emit('tool_call', {
            tool: tool.name,
            args: tool.args,
            timestamp: new Date()
          });
          
          // Emit tool_result event when tool completes
          if (tool.result !== undefined) {
            console.log('[Backend] Tool result:', tool.name, tool.result);
            socket.emit('tool_result', {
              tool: tool.name,
              output: tool.result,
              error: tool.error,
              timestamp: new Date()
            });
            
            // Special handling for terminal output
            if ((tool.name === 'run_shell_command' || tool.name === 'execute_command') && tool.result) {
              console.log('[Backend] Emitting terminal-output for:', tool.args?.command);
              
              // Extract output from agentcli's result format
              let terminalOutput = '';
              if (typeof tool.result === 'object' && tool.result !== null) {
                // AgentCLI returns {llmContent, returnDisplay}
                terminalOutput = tool.result.returnDisplay || tool.result.output || tool.result.stdout || '';
                
                // Fallback to llmContent parsing if returnDisplay is empty
                if (!terminalOutput && tool.result.llmContent) {
                  const stdoutMatch = tool.result.llmContent.match(/Stdout:\s*(.+?)(?:\n|$)/);
                  if (stdoutMatch && stdoutMatch[1] !== '(empty)') {
                    terminalOutput = stdoutMatch[1];
                  }
                }
              } else if (typeof tool.result === 'string') {
                terminalOutput = tool.result;
              }
              
              socket.emit('terminal-output', {
                command: tool.args?.command || 'Unknown command',
                output: terminalOutput || '(No output)'
              });
            }
          }
          
          // Send updated file handles after tool use
          const handles = agentService.getFileHandles();
          socket.emit('file-handles', handles);
        },
        onStateChange: (state: any) => {
          console.log('[Backend] Emitting agent-state:', state);
          socket.emit('agent-state', state);
        },
        onArtifact: (artifact: any) => {
          console.log('[Backend] Emitting artifact:', artifact.title);
          socket.emit('artifact', artifact);
        },
        onComplete: (result: any) => {
          socket.emit('complete', result);
          // Send final file handles
          const handles = agentService.getFileHandles();
          socket.emit('file-handles', handles);
        }
      });
    } catch (error) {
      console.error('Error processing message:', error);
      socket.emit('error', { 
        message: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  });

  // Handle file uploads
  socket.on('file-upload', async (data: { filename: string; content: string }) => {
    console.log(`Received file upload: ${data.filename}`);
    
    try {
      // Convert base64 or string content to Buffer
      const contentBuffer = Buffer.from(data.content);
      
      // Create file handle with storage
      const handle = await fileHandleManager.createHandle(data.filename, contentBuffer);
      
      // Send handle info back to client
      socket.emit('file-uploaded', {
        success: true,
        handle: {
          handleId: handle.handleId,
          filename: handle.filename,
          absolutePath: handle.absolutePath,
          size: contentBuffer.length
        }
      });
      
      // Update file handles list
      const handles = agentService.getFileHandles();
      socket.emit('file-handles', handles);
      
      // Send system message about the upload
      socket.emit('stream', `\n📁 File uploaded successfully!\n- Filename: ${data.filename}\n- Handle: ${handle.handleId}\n- Size: ${contentBuffer.length} bytes\n\nYou can now use "${handle.handleId}" in commands instead of the full path.\n\n`);
      
    } catch (error) {
      console.error('Error handling file upload:', error);
      socket.emit('file-uploaded', {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

const PORT = process.env.PORT || 8080;

httpServer.listen(PORT, () => {
  console.log(`Trading agent backend running on port ${PORT}`);
  console.log(`WebSocket server ready for connections`);
});