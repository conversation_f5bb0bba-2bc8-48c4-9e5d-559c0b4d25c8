#!/bin/bash

# Start agentcli WebSocket server
echo "[AgentCL<PERSON>] Starting WebSocket server on port 3001..."

cd agentcli

# Make sure GEMINI_API_KEY is available
if [ -z "$GEMINI_API_KEY" ]; then
    echo "[AgentCLI] ERROR: GEMINI_API_KEY not set"
    exit 1
fi

# Run agentcli in API mode with checkpointing enabled
# This ensures that file modifications create automatic snapshots
node packages/cli/dist/index.js --api-mode --api-port 3001 --checkpointing