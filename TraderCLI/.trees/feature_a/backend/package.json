{"name": "tradercli-backend", "version": "1.0.0", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "Trading agent backend powered by agentcli", "dependencies": {"@google/genai": "^1.10.0", "@modelcontextprotocol/sdk": "^1.16.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.0.15", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tsx": "^4.20.3", "typescript": "^5.8.3"}}