# TraderCLI Checkpoint Integration

This document explains how TraderCLI integrates with <PERSON><PERSON><PERSON>'s built-in checkpoint/restore system.

## Overview

TraderCLI uses <PERSON><PERSON><PERSON>'s native checkpointing system instead of a custom session management solution. This provides:

- **Automatic Git snapshots** before file modifications
- **Conversation history preservation**
- **Tool call restoration**
- **Instant rollback capabilities**

## How It Works

### 1. Automatic Checkpointing

When checkpointing is enabled (which it is by default), the system automatically creates checkpoints:

- **Before file modifications**: Any `write_file` or `replace` tool calls trigger a checkpoint
- **Git snapshot**: Creates a commit in `~/.gemini/history/<project_hash>`
- **State preservation**: Saves conversation history and pending tool calls

### 2. File Structure

Checkpoints are stored in:
```
~/.gemini/tmp/<project_hash>/checkpoints/
├── 2025-01-23T10-00-00_000Z-trades.csv-write_file.json
├── 2025-01-23T10-15-00_000Z-analysis.py-replace.json
└── checkpoint-my-analysis.json  # Manual save
```

### 3. Using Checkpoints in TraderCLI

#### Through the Agent (Recommended)

Send these commands directly to the agent in your messages:

```bash
# List available checkpoints
/restore

# Restore a specific checkpoint
/restore 2025-01-23T10-00-00_000Z-trades.csv-write_file

# Save current conversation state
/chat save my-analysis

# Resume a saved conversation
/chat resume my-analysis

# List saved conversations
/chat list
```

#### Through WebSocket Events (Advanced)

The backend also emits checkpoint notifications:

```javascript
// Frontend receives notification when checkpoint is created
socket.on('checkpoint-available', (data) => {
  console.log(`Checkpoint created for ${data.toolName}`);
});
```

## Configuration

### Enable/Disable Checkpointing

1. **Via Command Line**:
   ```bash
   # Enable
   ./start-agentcli.sh  # Already includes --checkpointing flag
   
   # Disable (edit start-agentcli.sh and remove --checkpointing)
   ```

2. **Via Settings File** (`backend/agentcli/settings.json`):
   ```json
   {
     "checkpointing": {
       "enabled": true  // Set to false to disable
     }
   }
   ```

## Use Cases

### 1. Safe Experimentation

When analyzing trading data, checkpoints let you:
- Try different analysis approaches
- Revert if results are unsatisfactory
- Keep your data files intact

Example:
```
User: Analyze my trades and create a summary report
Agent: [Creates checkpoint before modifying files]
User: /restore  # If you want to undo the changes
```

### 2. Session Management

Save and resume analysis sessions:
```
User: /chat save friday-analysis
[Later...]
User: /chat resume friday-analysis
```

### 3. Debugging

If the agent makes an error:
1. Use `/restore` to see recent checkpoints
2. Restore to before the error occurred
3. Try a different approach

## Important Notes

1. **Git Repository**: The shadow Git repository at `~/.gemini/history/` is separate from your project's Git repo
2. **Storage**: Checkpoints consume disk space; old checkpoints can be manually cleaned
3. **Performance**: Checkpointing adds minimal overhead (< 100ms per checkpoint)
4. **File Handles**: Checkpoints preserve file handle mappings (@F1, @F2, etc.)

## Integration with File Upload System

When files are uploaded through TraderCLI:
1. File gets assigned a handle (e.g., @F1)
2. Any modifications to the file trigger a checkpoint
3. The checkpoint preserves both the file state and handle mapping
4. Restoring brings back the exact state, including file handles

## Troubleshooting

### Checkpoints Not Being Created

1. Verify checkpointing is enabled:
   - Check `settings.json`
   - Ensure `--checkpointing` flag is present

2. Check Git is available:
   ```bash
   git --version
   ```

3. Verify permissions on checkpoint directory:
   ```bash
   ls -la ~/.gemini/tmp/
   ```

### Cannot Restore Checkpoint

1. List available checkpoints:
   ```
   /restore
   ```

2. Use exact checkpoint name (copy-paste to avoid typos)

3. Check if files have been manually modified outside of the agent

## Benefits Over Custom Session System

1. **Proven reliability**: agentcli's checkpoint system is battle-tested
2. **Git integration**: Leverages Git's robust versioning
3. **Atomic operations**: All-or-nothing restoration
4. **Tool integration**: Works seamlessly with all agentcli tools
5. **No additional complexity**: Uses existing infrastructure

## Future Enhancements

- Checkpoint pruning policies
- Cross-session checkpoint sharing
- Checkpoint metadata search
- Visual checkpoint browser in frontend