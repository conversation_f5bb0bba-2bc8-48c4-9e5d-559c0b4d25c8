#!/bin/bash

# TraderCLI Start Script
# This script starts all services: AgentCLI, Backend, and Frontend

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Change to the TraderCLI directory
cd "$SCRIPT_DIR"

echo "🚀 Starting TraderCLI..."
echo ""

# Check if node_modules exist
if [ ! -d "node_modules" ] || [ ! -d "backend/node_modules" ] || [ ! -d "frontend/node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm run setup
    echo ""
fi

# Start services
echo "🤖 Starting AgentCLI on ws://localhost:3001"
echo "🔧 Starting backend on http://localhost:8080"
echo "🎨 Starting frontend on http://localhost:3000"
echo ""
echo "Press Ctrl+C to stop all services"
echo ""

npm run dev