#!/usr/bin/env node

/**
 * TraderCLI Manager Script
 * Manages frontend and backend services
 */

import { spawn } from 'child_process';
import { existsSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __dirname = dirname(fileURLToPath(import.meta.url));

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  red: '\x1b[31m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

class TraderCLIManager {
  constructor() {
    this.processes = [];
  }

  async checkDependencies() {
    const dirs = ['node_modules', 'backend/node_modules', 'frontend/node_modules'];
    const missing = dirs.filter(dir => !existsSync(join(__dirname, dir)));
    
    if (missing.length > 0) {
      log('📦 Installing dependencies...', colors.yellow);
      await this.runCommand('npm', ['run', 'setup']);
      log('✅ Dependencies installed', colors.green);
    }
  }

  runCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
      const proc = spawn(command, args, {
        stdio: 'inherit',
        shell: true,
        ...options
      });

      proc.on('close', (code) => {
        if (code === 0) resolve();
        else reject(new Error(`Command failed with code ${code}`));
      });
    });
  }

  async start() {
    log('🚀 Starting TraderCLI...', colors.bright + colors.green);
    log('');
    
    await this.checkDependencies();
    
    log('🔧 Backend: http://localhost:8080', colors.yellow);
    log('🎨 Frontend: http://localhost:3000', colors.cyan);
    log('');
    log('Press Ctrl+C to stop all services', colors.bright);
    log('');

    // Start both services
    const backend = spawn('npm', ['run', 'dev'], {
      cwd: join(__dirname, 'backend'),
      stdio: 'pipe',
      shell: true
    });

    const frontend = spawn('npm', ['run', 'dev'], {
      cwd: join(__dirname, 'frontend'),
      stdio: 'pipe',
      shell: true
    });

    // Prefix output
    backend.stdout.on('data', (data) => {
      process.stdout.write(`${colors.yellow}[backend]${colors.reset} ${data}`);
    });

    backend.stderr.on('data', (data) => {
      process.stderr.write(`${colors.red}[backend]${colors.reset} ${data}`);
    });

    frontend.stdout.on('data', (data) => {
      process.stdout.write(`${colors.cyan}[frontend]${colors.reset} ${data}`);
    });

    frontend.stderr.on('data', (data) => {
      process.stderr.write(`${colors.red}[frontend]${colors.reset} ${data}`);
    });

    this.processes = [backend, frontend];

    // Handle shutdown
    process.on('SIGINT', () => this.stop());
    process.on('SIGTERM', () => this.stop());
  }

  stop() {
    log('\n🛑 Stopping TraderCLI services...', colors.red);
    
    this.processes.forEach(proc => {
      if (proc && !proc.killed) {
        proc.kill('SIGTERM');
      }
    });

    // Force kill after timeout
    setTimeout(() => {
      this.processes.forEach(proc => {
        if (proc && !proc.killed) {
          proc.kill('SIGKILL');
        }
      });
      log('✅ All services stopped', colors.green);
      process.exit(0);
    }, 1000);
  }

  async build() {
    log('🔨 Building TraderCLI...', colors.bright + colors.blue);
    
    await this.checkDependencies();
    
    log('Building backend...', colors.yellow);
    await this.runCommand('npm', ['run', 'build'], {
      cwd: join(__dirname, 'backend')
    });
    
    log('Building frontend...', colors.cyan);
    await this.runCommand('npm', ['run', 'build'], {
      cwd: join(__dirname, 'frontend')
    });
    
    log('✅ Build complete', colors.green);
  }
}

// CLI handling
const manager = new TraderCLIManager();
const command = process.argv[2];

switch (command) {
  case 'start':
  case 'dev':
    manager.start();
    break;
  case 'stop':
    manager.stop();
    break;
  case 'build':
    manager.build();
    break;
  default:
    log('TraderCLI Manager', colors.bright);
    log('');
    log('Usage:');
    log('  node run.js start   - Start development servers');
    log('  node run.js build   - Build for production');
    log('  node run.js stop    - Stop all services');
    process.exit(0);
}