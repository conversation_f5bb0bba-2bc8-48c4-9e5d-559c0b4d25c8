# Contributing to TraderIntelli

Thank you for your interest in contributing to TraderIntelli! We welcome contributions from the trading and developer communities.

## 🤝 Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct:
- Be respectful and inclusive
- Welcome newcomers and help them get started
- Focus on what is best for the community
- Show empathy towards other community members

## 🚀 Getting Started

1. Fork the repository
2. Clone your fork:
   ```bash
   git clone https://github.com/YOUR-USERNAME/traderintelli.git
   cd traderintelli
   ```
3. Add upstream remote:
   ```bash
   git remote add upstream https://github.com/claudesfortraders/traderintelli.git
   ```
4. Create a new branch:
   ```bash
   git checkout -b feature/your-feature-name
   ```

## 📋 Development Process

### 1. Before You Start

- Check existing issues and PRs to avoid duplicates
- For major changes, open an issue first to discuss
- Ensure you have Node.js 18+ and Git installed

### 2. Development Setup

```bash
# Install dependencies
npm run setup

# Set up environment
cp backend/.env.example backend/.env
# Add your GEMINI_API_KEY

# Start development
npm run dev
```

### 3. Making Changes

#### Code Style

- **TypeScript**: No `any` types, use `unknown` and type narrowing
- **React**: Functional components only, no class components
- **Testing**: Write real tests with actual data, no mocks
- **Comments**: Only high-value comments explaining "why"

#### File Structure

```
src/
├── services/     # Business logic
├── components/   # UI components
├── hooks/       # React hooks
├── tools/       # Trading analysis tools
└── types/       # TypeScript definitions
```

### 4. Testing

```bash
# Run all tests
npm run test

# Run specific test suite
npm run test:backend
npm run test:frontend

# Test with coverage
npm run test:coverage
```

#### Testing Requirements

- Test with real trading data (CSV/JSON files)
- Test actual broker integrations, not mocks
- Include edge cases traders actually face
- Ensure mathematical accuracy for all calculations

### 5. Commit Messages

Follow conventional commits:

```
feat: add MetaTrader 5 CSV parser
fix: correct Sharpe ratio calculation
docs: update API documentation
test: add OANDA CSV import tests
refactor: optimize pattern detection algorithm
```

### 6. Submitting a Pull Request

1. Update your branch:
   ```bash
   git fetch upstream
   git rebase upstream/main
   ```

2. Run tests and linting:
   ```bash
   npm run test
   npm run lint
   npm run typecheck
   ```

3. Push to your fork:
   ```bash
   git push origin feature/your-feature-name
   ```

4. Create a PR with:
   - Clear title and description
   - Link to related issue (if any)
   - Screenshots/videos for UI changes
   - Test results for algorithm changes

## 🎯 Areas for Contribution

### High Priority

- **Broker Integrations**: MT4/MT5, cTrader, TWS
- **Analysis Tools**: New pattern detection algorithms
- **Performance**: Optimization for large datasets
- **Documentation**: Tutorials, API docs, examples

### Good First Issues

- CSV parser improvements
- UI/UX enhancements
- Test coverage expansion
- Documentation updates

### Feature Requests

Check our [Roadmap](README.md#-roadmap) and [Issues](https://github.com/claudesfortraders/traderintelli/issues) for planned features.

## 🛠️ Technical Guidelines

### Frontend (React/Next.js)

- Use functional components with hooks
- Keep components pure during rendering
- Avoid `useEffect` for "do this when this changes"
- Use TypeScript interfaces over classes

### Backend (Node.js/Express)

- Keep services stateless
- Use dependency injection
- Handle errors gracefully
- Log important operations

### Trading Analysis

- Use institutional-grade formulas
- 252 trading days for annualization
- Include transaction costs in calculations
- Handle timezone conversions properly

## 📝 Documentation

When adding new features:

1. Update relevant README sections
2. Add JSDoc comments to functions
3. Create examples in `docs/`
4. Update API documentation

## 🐛 Reporting Issues

Use GitHub Issues with:

- Clear, descriptive title
- Steps to reproduce
- Expected vs actual behavior
- Environment details (OS, Node version)
- Sample data (if applicable)

## 💡 Feature Requests

Open an issue with:

- Problem description
- Proposed solution
- Alternative solutions considered
- Impact on existing features

## 📊 Performance Considerations

- Test with datasets of 10,000+ trades
- Optimize for real-time analysis
- Consider memory usage for large files
- Profile before optimizing

## 🔒 Security

- Never commit API keys or secrets
- Validate all user inputs
- Sanitize file uploads
- Use parameterized queries

## 📜 License

By contributing, you agree that your contributions will be licensed under the MIT License.

## 🙏 Recognition

Contributors will be:
- Listed in CONTRIBUTORS.md
- Mentioned in release notes
- Given credit in documentation

## 💬 Questions?

- Open a discussion on GitHub
- Join our Discord (coming soon)
- Email: <EMAIL>

---

Thank you for helping make TraderIntelli better for traders worldwide! 🚀