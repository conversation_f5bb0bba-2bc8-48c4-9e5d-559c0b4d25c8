# TraderIntelli (TraderCLI) 🚀

> AI-powered terminal interface for trading performance analysis. Understand why you lose money and get actionable insights.

**🔒 Private Repository** - This is proprietary software under active development.

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)](https://www.typescriptlang.org/)

## 🎯 Problem We Solve

15 million+ active traders globally struggle with:
- **Broker API sync problems** - Our #1 focus
- **Expensive AI insights** locked behind $80-100/month tiers
- **"Why do I lose money?"** - The question every trader asks

Trader<PERSON>ntelli provides institutional-grade analysis at $15-30/month.

## ✨ Features

### 🔥 Core Features
- **Drag & Drop Trade Import** - CSV/JSON files with smart parsing
- **AI-Powered Analysis** - Powered by Google's Gemini 2.5 Pro
- **Pattern Recognition** - "You lose 73% of trades after 2PM"
- **File Handle System** - Use `@F1` instead of long file paths
- **Checkpoint/Restore** - Undo any analysis with Git snapshots

### 📊 Analysis Capabilities
- Time-based performance analysis
- Symbol-specific win/loss patterns
- Psychological pattern detection (revenge trading, overtrading)
- Risk management insights
- Custom metric calculations

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Git
- Gemini API key from [Google AI Studio](https://aistudio.google.com/)

### Installation

```bash
# Clone the repository
git clone https://github.com/claudesfortraders/traderintelli.git
cd traderintelli

# Install dependencies
npm run setup

# Set up environment
echo "GEMINI_API_KEY=your-api-key-here" > backend/.env

# Start all services
npm run dev
```

Open http://localhost:3000 in your browser.

## 🛠️ Architecture

```
TraderIntelli/
├── frontend/          # Next.js terminal UI (port 3000)
│   └── src/
│       ├── app/      # App router
│       ├── components/   # Terminal components
│       └── hooks/       # React hooks
├── backend/          # Express + Socket.io API (port 8080)
│   ├── src/
│   │   ├── services/    # Core services
│   │   └── tools/       # Trading analysis tools
│   └── agentcli/     # Google's Gemini CLI (port 3001)
└── docs/             # Documentation
```

## 📖 Usage

### Basic Trading Analysis

1. **Upload your trades**:
   - Drag & drop CSV/JSON files into the terminal
   - Or type `upload` to show the upload zone

2. **Analyze with natural language**:
   ```
   "Analyze my trading patterns in @F1"
   "Show me when I lose money most often"
   "What are my worst trading habits?"
   ```

3. **Use checkpoints**:
   ```
   /restore              # List checkpoints
   /chat save my-analysis    # Save session
   /chat resume my-analysis  # Resume later
   ```

### File Handle System

When you upload files, they get handles:
```bash
# Instead of:
python3 /long/path/to/OANDA_trades_2024.csv

# Use:
python3 @F1
```

## 🔧 Development

### Commands

```bash
# Development
npm run dev           # Start all services
npm run stop         # Stop all services

# Individual services
npm run dev:backend   # Backend only
npm run dev:frontend  # Frontend only

# Testing
npm run test         # Run all tests
npm run test:backend # Backend tests only

# Building
npm run build        # Build all
npm run build:prod   # Production build
```

### Project Structure

- **Frontend**: Next.js 14 with TypeScript, React terminal components
- **Backend**: Express.js API with Socket.io for real-time communication
- **AgentCLI**: Forked Google Gemini CLI for AI analysis
- **Storage**: User-isolated file storage with 50MB limits

## 🔐 Security

- User file isolation
- 50MB file size limits
- Allowed formats: CSV, JSON, TXT, Excel
- No executable uploads
- Separate Git repos for checkpoints

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for details.

### Development Philosophy

- **Real tests** with actual trading data
- **No mocks** - test real broker integrations
- **User-first** - solve actual trader problems
- **Performance** - institutional-grade calculations

## 📊 Market Opportunity

- **Market Size**: $11.3B trading software market
- **Target Users**: 10M+ MetaTrader users
- **Competition**: TradingView ($15-60), TraderSync ($30-80)
- **Our Edge**: AI insights at fraction of cost

## 🗺️ Roadmap

### Phase 1 (Current)
- [x] File upload system
- [x] Basic AI analysis
- [x] Checkpoint/restore
- [ ] MetaTrader CSV support

### Phase 2
- [ ] Live broker connections (MCP)
- [ ] Advanced pattern detection
- [ ] Mobile app
- [ ] Team collaboration

### Phase 3
- [ ] Institutional features
- [ ] Custom AI training
- [ ] API access
- [ ] White-label solution

## 📝 License

MIT License - see [LICENSE](LICENSE) file.

## 🙏 Acknowledgments

- Built on [Google's Gemini CLI](https://github.com/google/generative-ai-js)
- Inspired by the 90% of traders who lose money
- Special thanks to the trading community

## 📞 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/claudesfortraders/traderintelli/issues)
- **Discord**: Coming soon

---

**Built for traders, by traders** 📈 Made with ❤️ by [@claudesfortraders](https://github.com/claudesfortraders)