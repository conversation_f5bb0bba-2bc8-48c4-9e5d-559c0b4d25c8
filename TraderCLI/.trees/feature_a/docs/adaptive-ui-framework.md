# TraderCLI Adaptive UI Framework Research & Implementation Plan

## Executive Summary

TraderCLI requires a modern, adaptive UI framework that transforms the current terminal-based interface into a dynamic trading journal with AI chat capabilities. Based on comprehensive research, we recommend implementing a **split-screen architecture** using **Next.js 15.4.1** with **React Server Components**, **TradingView Lightweight Charts** for visualizations, and a custom **Agent UI Protocol** for real-time adaptive content generation.

## Recommended Technology Stack

### Core Framework
- **Next.js 15.4.1** with App Router
- **React 19.1.0** 
- **TypeScript 5.x** for type safety
- **Tailwind CSS v4** for styling

### UI Components & Visualization
- **shadcn/ui** - Copy-paste component architecture for maximum flexibility
- **TradingView Lightweight Charts** - 45KB, WebGL-based financial charts
- **Recharts** - General metrics and dashboards
- **react-financial-charts** - Specialized trading indicators

### Agent UI Infrastructure
- **Custom Agent UI Protocol** - JSON-based schema for dynamic component generation
- **React JSON Schema Form (RJSF)** - For form generation from agent responses
- **WebSocket (Socket.io)** - Real-time bidirectional communication
- **Zustand** - Client state management

## Agent UI Protocol Design

### 1. Message Structure

```typescript
interface AgentMessage {
  id: string;
  type: 'text' | 'artifact' | 'chart' | 'metrics' | 'form' | 'action';
  content: AgentContent;
  metadata: {
    timestamp: Date;
    confidence?: number;
    source?: string;
  };
}

interface AgentContent {
  // For text responses
  text?: string;
  
  // For artifacts (like code, analysis)
  artifact?: {
    type: 'code' | 'analysis' | 'report';
    language?: string;
    content: string;
    editable?: boolean;
  };
  
  // For charts
  chart?: {
    type: 'candlestick' | 'line' | 'bar' | 'heatmap' | 'equity-curve';
    data: any;
    config: ChartConfig;
    interactive?: boolean;
  };
  
  // For metrics cards
  metrics?: {
    cards: MetricCard[];
    layout?: 'grid' | 'list' | 'carousel';
  };
  
  // For dynamic forms
  form?: {
    schema: JSONSchema7;
    uiSchema?: object;
    onSubmit?: string; // Action ID
  };
  
  // For actions (buttons, links)
  actions?: {
    primary?: ActionButton;
    secondary?: ActionButton[];
  };
}

interface MetricCard {
  title: string;
  value: string | number;
  change?: {
    value: number;
    percentage: number;
    direction: 'up' | 'down';
  };
  sparkline?: number[];
  unit?: string;
  color?: 'success' | 'danger' | 'warning' | 'info';
}
```

### 2. Backend Integration Pattern

```typescript
// backend/src/services/agent-ui.service.ts
export class AgentUIService {
  generateUIResponse(query: string, context: TradingContext): AgentMessage[] {
    // Analyze query intent
    const intent = this.analyzeIntent(query);
    
    // Generate appropriate UI components
    switch(intent.type) {
      case 'performance-analysis':
        return this.generatePerformanceUI(context);
      
      case 'trade-pattern':
        return this.generatePatternAnalysisUI(context);
      
      case 'risk-assessment':
        return this.generateRiskUI(context);
      
      default:
        return this.generateTextResponse(query);
    }
  }
  
  private generatePerformanceUI(context: TradingContext): AgentMessage[] {
    return [
      {
        id: uuid(),
        type: 'metrics',
        content: {
          metrics: {
            cards: [
              {
                title: 'Win Rate',
                value: context.winRate,
                change: { value: 2.3, percentage: 3.5, direction: 'up' },
                unit: '%',
                color: 'success'
              },
              // ... more metrics
            ]
          }
        },
        metadata: { timestamp: new Date() }
      },
      {
        id: uuid(),
        type: 'chart',
        content: {
          chart: {
            type: 'equity-curve',
            data: context.equityCurve,
            config: { responsive: true, theme: 'dark' }
          }
        },
        metadata: { timestamp: new Date() }
      }
    ];
  }
}
```

### 3. Frontend Dynamic Renderer

```typescript
// frontend/src/components/AgentUI/DynamicRenderer.tsx
'use client';

import { Suspense, lazy } from 'react';
import { AgentMessage } from '@/types/agent';

// Lazy load heavy components
const ChartRenderer = lazy(() => import('./renderers/ChartRenderer'));
const MetricsRenderer = lazy(() => import('./renderers/MetricsRenderer'));
const FormRenderer = lazy(() => import('./renderers/FormRenderer'));

export function DynamicRenderer({ message }: { message: AgentMessage }) {
  const renderContent = () => {
    switch (message.type) {
      case 'text':
        return <TextRenderer content={message.content.text} />;
      
      case 'artifact':
        return <ArtifactRenderer artifact={message.content.artifact} />;
      
      case 'chart':
        return (
          <Suspense fallback={<ChartSkeleton />}>
            <ChartRenderer chart={message.content.chart} />
          </Suspense>
        );
      
      case 'metrics':
        return (
          <Suspense fallback={<MetricsSkeleton />}>
            <MetricsRenderer metrics={message.content.metrics} />
          </Suspense>
        );
      
      case 'form':
        return (
          <Suspense fallback={<FormSkeleton />}>
            <FormRenderer form={message.content.form} />
          </Suspense>
        );
      
      case 'action':
        return <ActionRenderer actions={message.content.actions} />;
      
      default:
        return null;
    }
  };
  
  return (
    <div className="agent-message" data-message-id={message.id}>
      {renderContent()}
    </div>
  );
}
```

## UI Architecture

### 1. Layout Structure

```typescript
// frontend/src/app/dashboard/layout.tsx
export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="h-screen bg-gray-950 text-gray-100">
      {/* Top Navigation */}
      <TopNav />
      
      {/* Main Content Area */}
      <div className="flex h-[calc(100vh-64px)]">
        {/* Collapsible Sidebar */}
        <Sidebar />
        
        {/* Dashboard Content */}
        <main className="flex-1 overflow-hidden">
          {children}
        </main>
        
        {/* AI Chat Panel */}
        <AgentChatPanel />
      </div>
    </div>
  );
}
```

### 2. Split-Screen Dashboard

```typescript
// frontend/src/app/dashboard/page.tsx
export default function Dashboard() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-[1fr_400px] h-full">
      {/* Main Dashboard Area */}
      <div className="overflow-y-auto p-6 space-y-6">
        {/* Performance Metrics */}
        <MetricsGrid />
        
        {/* Interactive Charts */}
        <ChartsSection />
        
        {/* Trade Journal Table */}
        <TradeJournal />
      </div>
      
      {/* AI Assistant (Desktop Only) */}
      <div className="hidden lg:block border-l border-gray-800">
        <AgentChat />
      </div>
    </div>
  );
}
```

### 3. Responsive Design Strategy

```typescript
// frontend/src/hooks/useResponsive.ts
export function useResponsive() {
  const [device, setDevice] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  
  useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth;
      if (width < 768) setDevice('mobile');
      else if (width < 1024) setDevice('tablet');
      else setDevice('desktop');
    };
    
    checkDevice();
    window.addEventListener('resize', checkDevice);
    return () => window.removeEventListener('resize', checkDevice);
  }, []);
  
  return {
    device,
    isMobile: device === 'mobile',
    isTablet: device === 'tablet',
    isDesktop: device === 'desktop',
  };
}
```

## Implementation Roadmap

### Phase 1: Foundation (Week 1-2)
1. Set up new UI architecture with shadcn/ui
2. Implement split-screen layout with responsive design
3. Create base Agent UI protocol and message types
4. Set up WebSocket streaming for real-time updates

### Phase 2: Core Components (Week 3-4)
1. Implement TradingView Lightweight Charts integration
2. Build metrics dashboard with real-time updates
3. Create dynamic component renderer system
4. Implement chat interface with streaming responses

### Phase 3: Advanced Features (Week 5-6)
1. Add interactive chart analysis tools
2. Implement form generation from agent responses
3. Create artifact viewer (code, reports, analysis)
4. Add state persistence and session management

### Phase 4: Polish & Optimization (Week 7-8)
1. Implement dark/light theme system
2. Add animations and transitions
3. Optimize performance for large datasets
4. Implement comprehensive error handling

## Security Considerations

### 1. Dynamic Component Rendering
```typescript
// Sanitize all agent-generated content
import DOMPurify from 'isomorphic-dompurify';

export function sanitizeAgentContent(content: string): string {
  return DOMPurify.sanitize(content, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'code', 'pre'],
    ALLOWED_ATTR: ['class', 'style']
  });
}
```

### 2. Content Security Policy
```typescript
// next.config.ts
const securityHeaders = [
  {
    key: 'Content-Security-Policy',
    value: "default-src 'self'; script-src 'self' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
  }
];
```

## Performance Optimization

### 1. Chart Data Aggregation
```typescript
// Downsample large datasets for better performance
export function downsampleChartData(
  data: ChartDataPoint[],
  maxPoints: number = 1000
): ChartDataPoint[] {
  if (data.length <= maxPoints) return data;
  
  const ratio = Math.ceil(data.length / maxPoints);
  return data.filter((_, index) => index % ratio === 0);
}
```

### 2. Memoization Strategy
```typescript
// Memoize expensive calculations
const MemoizedChart = memo(({ data, config }: ChartProps) => {
  const processedData = useMemo(() => 
    processChartData(data), [data]
  );
  
  return <TradingViewChart data={processedData} config={config} />;
});
```

## Conclusion

This adaptive UI framework provides TraderCLI with a modern, flexible foundation for displaying AI-generated trading insights. The combination of React Server Components, TradingView charts, and a custom Agent UI protocol enables dynamic, real-time visualization of trading performance metrics while maintaining excellent performance and user experience.

The split-screen architecture allows traders to interact with the AI assistant while simultaneously viewing their performance data, creating a seamless analytical workflow. The JSON-based Agent UI protocol ensures that the Gemini backend can generate rich, interactive UI components without being tightly coupled to the frontend implementation.

Next steps involve implementing the foundation components and gradually building out the more advanced features, always keeping the focus on providing actionable insights that help traders understand and improve their performance.