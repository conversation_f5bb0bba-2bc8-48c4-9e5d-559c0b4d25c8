# TraderCLI Checkpoint System Demo

This demo shows how to use <PERSON><PERSON><PERSON>'s checkpoint/restore system within TraderCLI.

## Demo Scenario: Trading Analysis with Rollback

### Step 1: Upload Trading Data

1. Drag and drop your trading CSV file into the terminal
2. The system assigns it handle `@F1`
3. Type: "Analyze my trading patterns in @F1"

### Step 2: Checkpoint is Created Automatically

When the agent modifies or creates files, you'll see:
```
📁 File uploaded successfully!
- Handle: @F1
- Size: 1899 bytes

[Agent creates analysis...]
✓ Checkpoint created before file modification
```

### Step 3: Review and Potentially Rollback

If you're not happy with the analysis:
```
User: /restore
Agent: Available checkpoints:
  - 2025-01-23T10-00-00_000Z-analysis.py-write_file
  - 2025-01-23T09-45-00_000Z-trades.csv-write_file

User: /restore 2025-01-23T10-00-00_000Z-analysis.py-write_file
Agent: ✓ Restored to checkpoint. Original tool call restored.
```

### Step 4: Save Analysis Session

To save your current analysis state:
```
User: /chat save nasdaq-analysis-v2
Agent: ✓ Conversation saved as 'nasdaq-analysis-v2'
```

### Step 5: Resume Later

Come back anytime:
```
User: /chat resume nasdaq-analysis-v2
Agent: ✓ Resumed conversation 'nasdaq-analysis-v2'
[Previous context restored]
```

## Advanced Usage

### Experimenting with Different Approaches

```
User: Create a performance report for @F1
[Agent creates report_v1.pdf]

User: Actually, can you make it more detailed with charts?
[Agent modifies report → checkpoint created]

User: Hmm, I preferred the simpler version. /restore
[Reverts to report_v1.pdf]
```

### Managing Multiple Analysis Sessions

```
User: /chat list
Agent: Saved conversations:
  - nasdaq-analysis-v2
  - forex-backtesting
  - options-strategy-test

User: /chat resume forex-backtesting
[Switches context completely]
```

## Tips

1. **Checkpoints are automatic** - You don't need to manually create them before file changes
2. **Use descriptive tags** - When saving conversations, use meaningful names
3. **Checkpoints persist** - They survive terminal restarts
4. **No Git conflicts** - The checkpoint Git repo is separate from your project

## Integration with File Handles

The checkpoint system preserves file handle mappings:
- If you had `@F1 → trades.csv` before checkpoint
- After restore, `@F1` still points to the same file
- No need to re-upload files after restoration