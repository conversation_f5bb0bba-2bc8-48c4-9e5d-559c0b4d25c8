# TraderCLI Adaptive UI Implementation Example

This document provides a step-by-step example of transforming the current terminal UI into the new adaptive framework.

## Step 1: Install Required Dependencies

```bash
cd /Applications/AI Project /myclaude/TraderCLI/frontend
npm install lightweight-charts recharts react-financial-charts @rjsf/core @rjsf/utils @rjsf/validator-ajv8 zustand framer-motion
npm install -D @types/d3
```

## Step 2: Create New Layout Structure

### Create the Dashboard Layout

```typescript
// frontend/src/app/dashboard/layout.tsx
import { Suspense } from 'react';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="h-screen bg-gray-950 text-gray-100 overflow-hidden">
      {/* Top Navigation Bar */}
      <header className="h-16 bg-gray-900 border-b border-gray-800 px-6 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-bold">TraderCLI</h1>
          <nav className="flex space-x-4">
            <a href="/dashboard" className="text-gray-400 hover:text-white">Dashboard</a>
            <a href="/journal" className="text-gray-400 hover:text-white">Journal</a>
            <a href="/analytics" className="text-gray-400 hover:text-white">Analytics</a>
          </nav>
        </div>
        <div className="flex items-center space-x-4">
          <button className="text-gray-400 hover:text-white">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
          </button>
          <button className="text-gray-400 hover:text-white">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </button>
        </div>
      </header>
      
      {/* Main Content */}
      <main className="h-[calc(100vh-4rem)]">
        <Suspense fallback={<LoadingScreen />}>
          {children}
        </Suspense>
      </main>
    </div>
  );
}

function LoadingScreen() {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>
  );
}
```

### Create the Main Dashboard Page

```typescript
// frontend/src/app/dashboard/page.tsx
'use client';

import { useState } from 'react';
import { MetricsGrid } from '@/components/Dashboard/MetricsGrid';
import { TradingChart } from '@/components/Dashboard/TradingChart';
import { AgentChat } from '@/components/Agent/AgentChat';
import { useAgent } from '@/hooks/useAgent';

export default function Dashboard() {
  const [isChatOpen, setIsChatOpen] = useState(true);
  
  return (
    <div className="flex h-full">
      {/* Main Dashboard Content */}
      <div className={`flex-1 transition-all duration-300 ${isChatOpen ? 'mr-96' : ''}`}>
        <div className="h-full overflow-y-auto p-6 space-y-6">
          {/* Performance Metrics */}
          <MetricsGrid />
          
          {/* Trading Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <TradingChart 
              title="Equity Curve" 
              type="equity-curve"
              height={400}
            />
            <TradingChart 
              title="Win/Loss Distribution" 
              type="heatmap"
              height={400}
            />
          </div>
          
          {/* Recent Trades Table */}
          <RecentTradesTable />
        </div>
      </div>
      
      {/* AI Chat Panel */}
      <div className={`fixed right-0 top-16 bottom-0 w-96 bg-gray-900 border-l border-gray-800 transition-transform duration-300 ${
        isChatOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        <AgentChat onClose={() => setIsChatOpen(false)} />
      </div>
      
      {/* Chat Toggle Button (when closed) */}
      {!isChatOpen && (
        <button
          onClick={() => setIsChatOpen(true)}
          className="fixed right-4 bottom-4 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-4 shadow-lg"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
          </svg>
        </button>
      )}
    </div>
  );
}
```

## Step 3: Create Core Components

### Metrics Grid Component

```typescript
// frontend/src/components/Dashboard/MetricsGrid.tsx
import { MetricCard } from './MetricCard';
import { useTrading } from '@/hooks/useTrading';

export function MetricsGrid() {
  const { metrics } = useTrading();
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <MetricCard
        title="Win Rate"
        value={metrics.winRate}
        unit="%"
        change={{ value: 2.3, percentage: 3.5, direction: 'up' }}
        color="success"
      />
      <MetricCard
        title="Profit Factor"
        value={metrics.profitFactor}
        change={{ value: 0.12, percentage: 5.4, direction: 'up' }}
        color="success"
      />
      <MetricCard
        title="Sharpe Ratio"
        value={metrics.sharpeRatio}
        change={{ value: -0.05, percentage: -2.6, direction: 'down' }}
        color="warning"
      />
      <MetricCard
        title="Total P&L"
        value={`$${metrics.totalPnL.toLocaleString()}`}
        change={{ value: 1245, percentage: 11.1, direction: 'up' }}
        color="success"
      />
    </div>
  );
}
```

### Trading Chart Component with TradingView

```typescript
// frontend/src/components/Dashboard/TradingChart.tsx
'use client';

import { useEffect, useRef } from 'react';
import { createChart, IChartApi } from 'lightweight-charts';

interface TradingChartProps {
  title: string;
  type: 'equity-curve' | 'candlestick' | 'heatmap';
  height?: number;
  data?: any[];
}

export function TradingChart({ title, type, height = 400, data }: TradingChartProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  
  useEffect(() => {
    if (!chartContainerRef.current) return;
    
    chartRef.current = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height,
      layout: {
        backgroundColor: '#111827',
        textColor: '#9CA3AF',
      },
      grid: {
        vertLines: { color: '#1F2937' },
        horzLines: { color: '#1F2937' },
      },
    });
    
    // Add series based on type
    if (type === 'equity-curve') {
      const lineSeries = chartRef.current.addLineSeries({
        color: '#3B82F6',
        lineWidth: 2,
      });
      
      // Add sample data (replace with real data)
      lineSeries.setData([
        { time: '2024-01-01', value: 10000 },
        { time: '2024-01-15', value: 10500 },
        { time: '2024-02-01', value: 11200 },
        { time: '2024-02-15', value: 10800 },
        { time: '2024-03-01', value: 12450 },
      ]);
    }
    
    // Handle resize
    const handleResize = () => {
      if (chartRef.current && chartContainerRef.current) {
        chartRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
        });
      }
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      chartRef.current?.remove();
    };
  }, [type, height]);
  
  return (
    <div className="bg-gray-900 rounded-lg p-4">
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <div ref={chartContainerRef} />
    </div>
  );
}
```

### Agent Chat with Dynamic UI Rendering

```typescript
// frontend/src/components/Agent/AgentChat.tsx
'use client';

import { useState, useRef, useEffect } from 'react';
import { useAgent } from '@/hooks/useAgent';
import { AgentMessage } from '@/types/agent';
import { DynamicRenderer } from './DynamicRenderer';

interface AgentChatProps {
  onClose?: () => void;
}

export function AgentChat({ onClose }: AgentChatProps) {
  const [messages, setMessages] = useState<AgentMessage[]>([]);
  const [input, setInput] = useState('');
  const { sendMessage, isProcessing, connectionStatus } = useAgent();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isProcessing) return;
    
    const userMessage: AgentMessage = {
      id: Date.now().toString(),
      type: 'text',
      content: { text: input },
      metadata: { timestamp: new Date() }
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    
    try {
      await sendMessage(input, {
        onStream: (chunk) => {
          // Handle streaming responses
        },
        onMessage: (message: AgentMessage) => {
          setMessages(prev => [...prev, message]);
        }
      });
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };
  
  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-800 flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">AI Trading Assistant</h2>
          <p className="text-sm text-gray-400">
            {connectionStatus === 'connected' ? 'Online' : 'Connecting...'}
          </p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>
      
      {/* Quick Actions */}
      <div className="p-4 border-b border-gray-800">
        <p className="text-xs text-gray-500 mb-2">Quick Actions</p>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setInput("Analyze my trading performance today")}
            className="px-3 py-1 text-xs bg-gray-800 hover:bg-gray-700 rounded-full"
          >
            Today's Performance
          </button>
          <button
            onClick={() => setInput("Show me my worst performing trades")}
            className="px-3 py-1 text-xs bg-gray-800 hover:bg-gray-700 rounded-full"
          >
            Loss Analysis
          </button>
          <button
            onClick={() => setInput("What patterns do you see in my trading?")}
            className="px-3 py-1 text-xs bg-gray-800 hover:bg-gray-700 rounded-full"
          >
            Pattern Detection
          </button>
        </div>
      </div>
      
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <DynamicRenderer key={message.id} message={message} />
        ))}
        {isProcessing && (
          <div className="flex items-center space-x-2 text-gray-400">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
            <span className="text-sm">Analyzing...</span>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>
      
      {/* Input */}
      <form onSubmit={handleSubmit} className="p-4 border-t border-gray-800">
        <div className="flex space-x-2">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask about your trading performance..."
            className="flex-1 bg-gray-800 text-white px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isProcessing}
          />
          <button
            type="submit"
            disabled={isProcessing || !input.trim()}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
}
```

## Step 4: Update the Backend Agent Service

```typescript
// backend/src/services/agent.service.ts
import { AgentMessage, TradingContext } from '../types/agent';

export class AgentService {
  async processMessage(message: string, context: TradingContext): Promise<AgentMessage[]> {
    // Analyze the intent
    const intent = this.analyzeIntent(message);
    
    // Generate appropriate UI responses
    switch (intent.type) {
      case 'performance_query':
        return this.generatePerformanceResponse(context);
      
      case 'pattern_analysis':
        return this.generatePatternAnalysis(context);
      
      case 'risk_assessment':
        return this.generateRiskAssessment(context);
      
      default:
        return [{
          id: this.generateId(),
          type: 'text',
          content: { text: await this.generateTextResponse(message) },
          metadata: { timestamp: new Date() }
        }];
    }
  }
  
  private generatePerformanceResponse(context: TradingContext): AgentMessage[] {
    return [
      {
        id: this.generateId(),
        type: 'text',
        content: {
          text: `Based on your trading data, here's your performance summary:`
        },
        metadata: { timestamp: new Date() }
      },
      {
        id: this.generateId(),
        type: 'metrics',
        content: {
          metrics: {
            cards: [
              {
                title: 'Win Rate',
                value: 67.5,
                unit: '%',
                change: { value: 2.3, percentage: 3.5, direction: 'up' },
                color: 'success'
              },
              {
                title: 'Average Win',
                value: 245.50,
                unit: '$',
                change: { value: 12.30, percentage: 5.3, direction: 'up' },
                color: 'success'
              },
              {
                title: 'Average Loss',
                value: -156.25,
                unit: '$',
                change: { value: -8.75, percentage: -5.9, direction: 'down' },
                color: 'danger'
              },
              {
                title: 'Profit Factor',
                value: 2.34,
                change: { value: 0.12, percentage: 5.4, direction: 'up' },
                color: 'success'
              }
            ],
            layout: 'grid'
          }
        },
        metadata: { timestamp: new Date() }
      },
      {
        id: this.generateId(),
        type: 'chart',
        content: {
          chart: {
            type: 'equity-curve',
            data: this.generateEquityCurveData(context),
            config: {
              responsive: true,
              theme: 'dark',
              height: 300
            }
          }
        },
        metadata: { timestamp: new Date() }
      }
    ];
  }
}
```

## Step 5: Update Socket.io Handler

```typescript
// backend/src/index.ts
io.on('connection', (socket) => {
  console.log('Client connected');
  
  socket.on('message', async ({ content, context }) => {
    try {
      const agentService = new AgentService();
      const responses = await agentService.processMessage(content, context);
      
      // Send each response as a separate message
      for (const response of responses) {
        socket.emit('agent-message', response);
      }
      
      socket.emit('complete', { success: true });
    } catch (error) {
      socket.emit('error', { message: error.message });
    }
  });
});
```

## Next Steps

1. **Install UI Component Library**: Set up shadcn/ui components
2. **Implement Chart Library**: Integrate TradingView Lightweight Charts
3. **Add State Management**: Set up Zustand for global state
4. **Create More Renderers**: Build renderers for forms, artifacts, and actions
5. **Add Animations**: Use Framer Motion for smooth transitions
6. **Implement Theme System**: Add dark/light mode toggle
7. **Mobile Responsive**: Create mobile-specific layouts
8. **Testing**: Add comprehensive tests for all components

This implementation provides a solid foundation for the adaptive UI framework while maintaining the existing backend integration through Socket.io.