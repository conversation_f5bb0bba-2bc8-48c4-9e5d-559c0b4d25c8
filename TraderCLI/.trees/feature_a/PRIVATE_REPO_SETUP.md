# Private Repository Setup Instructions

## IMPORTANT: This repository MUST be private!

### Before Pushing to GitHub

1. **Create a PRIVATE repository on GitHub**:
   - Go to https://github.com/new
   - Repository name: `traderintelli`
   - Description: "AI-powered trading performance analysis terminal"
   - **✅ Select "Private"** (NOT Public!)
   - Do NOT initialize with README, .gitignore, or license

2. **Verify it's private**:
   - After creation, you should see a 🔒 lock icon
   - Repository URL should show "Private" label

### Git Commands to Push

After creating the PRIVATE repository, run these commands:

```bash
# Add all files
git add .

# Create initial commit
git commit -m "Initial commit: TraderIntelli AI trading terminal

- Terminal UI with drag & drop file upload
- AI-powered analysis with Gemini 2.5 Pro
- File handle system (@F1, @F2)
- Checkpoint/restore functionality
- User-isolated storage system
- WebSocket real-time communication"

# Add remote (replace USERNAME if different)
git remote add origin https://github.com/claudesfortraders/traderintelli.git

# Push to private repository
git push -u origin main
```

### Verify Privacy After Push

1. Log out of GitHub (or use incognito)
2. Try to access: https://github.com/claudesfortraders/traderintelli
3. You should see "404 Not Found" if it's properly private

### Adding Collaborators

To add team members to the private repo:
1. Go to Settings → Manage access
2. Click "Add people"
3. Enter their GitHub username
4. Choose permission level

### Security Checklist

Before pushing, verify:
- [ ] No API keys in code (check .env files)
- [ ] No customer data in examples
- [ ] No hardcoded credentials
- [ ] Trading data files are in .gitignore
- [ ] All sensitive paths are excluded

### Converting to Public (Future)

If you ever want to make it public:
1. Remove all sensitive data
2. Review entire commit history
3. Settings → Change visibility → Make public

⚠️ **WARNING**: Once public, assume all code is permanently visible even if made private again!

---

**Remember**: This contains proprietary trading algorithms and customer data handling. Keep it PRIVATE!