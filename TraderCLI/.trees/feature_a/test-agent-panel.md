# Test Commands for Agent Panel

These commands will trigger different agent states:

1. **Searching State**: 
   ```
   search for "analyze" in the codebase
   ```

2. **Reading Files State**:
   ```
   show me the package.json file
   ```

3. **Writing Code State**:
   ```
   create a test.py file with a simple hello world
   ```

4. **Running Command State**:
   ```
   run ls -la
   ```

5. **Analyzing Data State**:
   ```
   analyze my CSV file
   ```

Try these commands in the terminal to see if the Agent Panel updates properly!