// Agent UI Protocol Types

export interface AgentMessage {
  id: string;
  type: 'text' | 'artifact' | 'chart' | 'metrics' | 'form' | 'action';
  content: AgentContent;
  metadata: {
    timestamp: Date;
    confidence?: number;
    source?: string;
  };
}

export interface AgentContent {
  // For text responses
  text?: string;
  
  // For artifacts (like code, analysis)
  artifact?: {
    type: 'code' | 'analysis' | 'report';
    language?: string;
    content: string;
    editable?: boolean;
  };
  
  // For charts
  chart?: {
    type: 'candlestick' | 'line' | 'bar' | 'heatmap' | 'equity-curve';
    data: any;
    config: ChartConfig;
    interactive?: boolean;
  };
  
  // For metrics cards
  metrics?: {
    cards: MetricCard[];
    layout?: 'grid' | 'list' | 'carousel';
  };
  
  // For dynamic forms
  form?: {
    schema: any; // JSON Schema
    uiSchema?: object;
    onSubmit?: string; // Action ID
  };
  
  // For actions (buttons, links)
  actions?: {
    primary?: ActionButton;
    secondary?: ActionButton[];
  };
}

export interface MetricCard {
  title: string;
  value: string | number;
  change?: {
    value: number;
    percentage: number;
    direction: 'up' | 'down';
  };
  sparkline?: number[];
  unit?: string;
  color?: 'success' | 'danger' | 'warning' | 'info';
}

export interface ChartConfig {
  responsive?: boolean;
  theme?: 'dark' | 'light';
  height?: number;
  showGrid?: boolean;
  showLegend?: boolean;
}

export interface ActionButton {
  label: string;
  action: string;
  variant?: 'primary' | 'secondary' | 'danger';
  icon?: string;
}

export interface TradingContext {
  accountId?: string;
  timeframe?: string;
  symbols?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  metrics?: {
    winRate: number;
    profitFactor: number;
    sharpeRatio: number;
    totalPnL: number;
  };
}