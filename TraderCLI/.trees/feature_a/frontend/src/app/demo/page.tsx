'use client';

import { useState, useEffect } from 'react';
import { DemoChart } from '@/components/Demo/DemoChart';
import { DemoMetrics } from '@/components/Demo/DemoMetrics';
import { DemoChat } from '@/components/Demo/DemoChat';
import { AgentMessage } from '@/types/agent';

// Demo data generator with deterministic seed
const generateEquityCurve = (seed = 1) => {
  const data = [];
  let value = 10000;
  const startDate = new Date('2024-01-01');
  
  // Simple deterministic random based on seed
  const seededRandom = (index: number) => {
    const x = Math.sin(seed + index) * 10000;
    return x - Math.floor(x);
  };
  
  for (let i = 0; i < 90; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    
    // Simulate realistic trading P&L with seeded random
    const dailyReturn = (seededRandom(i) - 0.48) * 0.02; // Slight edge
    value = value * (1 + dailyReturn);
    
    data.push({
      time: date.toISOString().split('T')[0],
      value: Math.round(value * 100) / 100
    });
  }
  
  return data;
};

export default function DemoPage() {
  const [messages, setMessages] = useState<AgentMessage[]>([]);
  const [metrics, setMetrics] = useState({
    winRate: 67.5,
    profitFactor: 2.34,
    sharpeRatio: 1.89,
    totalPnL: 12450
  });
  const [chartData, setChartData] = useState<any[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [livePrice, setLivePrice] = useState(186.42);
  const [activePosition, setActivePosition] = useState<{symbol: string; side: 'long' | 'short'; pnl: number} | null>(null);

  // Only run on client to avoid hydration mismatch
  useEffect(() => {
    setIsClient(true);
    setChartData(generateEquityCurve(1));
    // Simulate an active position
    setActivePosition({ symbol: 'AAPL', side: 'long', pnl: 145.20 });
  }, []);

  // Simulate real-time updates (only on client)
  useEffect(() => {
    if (!isClient) return;
    
    let updateCount = 0;
    
    // Price ticker update
    const priceInterval = setInterval(() => {
      updateCount++;
      const volatility = 0.002; // 0.2% volatility
      const trend = Math.sin(updateCount * 0.05) * 0.001;
      const noise = (Math.sin(updateCount * 1.7) * 0.5 + Math.sin(updateCount * 2.3) * 0.3) * volatility;
      
      setLivePrice(prev => {
        const newPrice = prev * (1 + trend + noise);
        return Math.round(newPrice * 100) / 100;
      });
      
      // Update position P&L
      if (activePosition) {
        setActivePosition(prev => {
          if (!prev) return null;
          const basePnL = 145.20;
          const variance = Math.sin(updateCount * 0.3) * 50 + (Math.random() - 0.5) * 20;
          return { ...prev, pnl: basePnL + variance };
        });
      }
    }, 1000);
    
    // Slower metric updates
    const metricInterval = setInterval(() => {
      setMetrics(prev => {
        const factor = Math.sin(updateCount * 0.05);
        const drift = updateCount * 0.001; // Slight upward drift
        return {
          winRate: Math.max(60, Math.min(75, 67.5 + factor * 3 + drift)),
          profitFactor: Math.max(1.5, Math.min(3, 2.34 + factor * 0.3)),
          sharpeRatio: Math.max(1, Math.min(2.5, 1.89 + factor * 0.2)),
          totalPnL: 12450 + factor * 800 + drift * 1000
        };
      });
    }, 5000);

    return () => {
      clearInterval(priceInterval);
      clearInterval(metricInterval);
    };
  }, [isClient, activePosition]);

  // Demo: Simulate agent response
  const simulateAgentResponse = async (query: string) => {
    setIsStreaming(true);
    
    // Add user message
    const userMsg: AgentMessage = {
      id: Date.now().toString(),
      type: 'text',
      content: { text: query },
      metadata: { timestamp: new Date() }
    };
    setMessages(prev => [...prev, userMsg]);

    // Simulate different responses based on query
    if (query.toLowerCase().includes('performance')) {
      // Text response
      const textMsg: AgentMessage = {
        id: (Date.now() + 1).toString(),
        type: 'text',
        content: { text: "Analyzing your trading performance..." },
        metadata: { timestamp: new Date() }
      };
      setMessages(prev => [...prev, textMsg]);

      await new Promise(resolve => setTimeout(resolve, 1000));

      // Metrics card response
      const metricsMsg: AgentMessage = {
        id: (Date.now() + 2).toString(),
        type: 'metrics',
        content: {
          metrics: {
            cards: [
              {
                title: 'Win Rate',
                value: 67.5,
                unit: '%',
                change: { value: 2.3, percentage: 3.5, direction: 'up' },
                color: 'success'
              },
              {
                title: 'Avg Win/Loss',
                value: 1.85,
                change: { value: 0.12, percentage: 6.9, direction: 'up' },
                color: 'success'
              },
              {
                title: 'Max Drawdown',
                value: -8.2,
                unit: '%',
                change: { value: -1.1, percentage: -15.5, direction: 'down' },
                color: 'danger'
              },
              {
                title: 'Trade Count',
                value: 156,
                change: { value: 12, percentage: 8.3, direction: 'up' },
                color: 'info'
              }
            ],
            layout: 'grid'
          }
        },
        metadata: { timestamp: new Date() }
      };
      setMessages(prev => [...prev, metricsMsg]);

      await new Promise(resolve => setTimeout(resolve, 1000));

      // Chart response
      const chartMsg: AgentMessage = {
        id: (Date.now() + 3).toString(),
        type: 'chart',
        content: {
          chart: {
            type: 'equity-curve',
            data: generateEquityCurve(2),
            config: {
              responsive: true,
              theme: 'dark',
              height: 300
            }
          }
        },
        metadata: { timestamp: new Date() }
      };
      setMessages(prev => [...prev, chartMsg]);

    } else if (query.toLowerCase().includes('pattern')) {
      // Pattern analysis response
      const analysisMsg: AgentMessage = {
        id: (Date.now() + 1).toString(),
        type: 'artifact',
        content: {
          artifact: {
            type: 'analysis',
            content: `## Trading Pattern Analysis

### Time-Based Patterns
- **Best Performance**: Tuesday mornings (9:30-11:00 AM)
- **Worst Performance**: Friday afternoons (2:00-4:00 PM)
- **Win Rate by Day**: Mon (65%), Tue (72%), Wed (68%), Thu (66%), Fri (58%)

### Symbol Performance
- **Most Profitable**: AAPL (+$3,450), NVDA (+$2,890), TSLA (+$2,100)
- **Least Profitable**: META (-$1,200), AMZN (-$890), GOOGL (-$450)

### Behavioral Patterns Detected
1. **Overtrading**: Average 8 trades on losing days vs 3 on winning days
2. **Revenge Trading**: 73% of losses followed by larger position sizes
3. **Early Exit Pattern**: Closing 68% of winning trades before target

### Recommendations
- Limit daily trades to 4 maximum
- Implement 30-minute cooldown after losses
- Set and respect profit targets`,
            editable: false
          }
        },
        metadata: { timestamp: new Date() }
      };
      setMessages(prev => [...prev, analysisMsg]);
    } else {
      // Default text response
      const responseMsg: AgentMessage = {
        id: (Date.now() + 1).toString(),
        type: 'text',
        content: { 
          text: "I can help you analyze your trading performance. Try asking about your 'performance metrics' or 'trading patterns'." 
        },
        metadata: { timestamp: new Date() }
      };
      setMessages(prev => [...prev, responseMsg]);
    }

    setIsStreaming(false);
  };

  return (
    <div className="min-h-screen bg-gray-950 text-gray-100">
      {/* Minimal Header */}
      <div className="bg-gray-900 border-b border-gray-800 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <h1 className="text-lg font-semibold">TraderCLI</h1>
            {isClient && activePosition && (
              <div className="flex items-center space-x-4 text-sm">
                <span className="text-gray-400">Position:</span>
                <span className="font-mono">{activePosition.symbol}</span>
                <span className={`px-2 py-0.5 rounded text-xs ${
                  activePosition.side === 'long' ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'
                }`}>
                  {activePosition.side.toUpperCase()}
                </span>
                <span className={`font-mono ${activePosition.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {activePosition.pnl >= 0 ? '+' : ''}${activePosition.pnl.toFixed(2)}
                </span>
              </div>
            )}
          </div>
          <div className="flex items-center space-x-4">
            {isClient && (
              <div className="flex items-center space-x-2 text-sm">
                <span className="text-gray-400">AAPL:</span>
                <span className="font-mono text-lg">${livePrice}</span>
                <span className={`text-xs ${livePrice > 186.42 ? 'text-green-400' : 'text-red-400'}`}>
                  {livePrice > 186.42 ? '▲' : '▼'}
                </span>
              </div>
            )}
            <button
              onClick={() => window.location.href = '/'}
              className="text-sm text-gray-400 hover:text-white transition-colors"
            >
              Exit Demo
            </button>
          </div>
        </div>
      </div>

      {/* Main Layout */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Dashboard Area */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Compact Metrics */}
          {isClient && <DemoMetrics metrics={metrics} />}

          {/* Live Chart */}
          <div className="mt-6">
            {isClient && <DemoChart data={chartData} height={300} />}
          </div>
        </div>

        {/* AI Chat Panel - Narrower */}
        <div className="w-80 bg-gray-900 border-l border-gray-800">
          <DemoChat 
            messages={messages}
            onSendMessage={simulateAgentResponse}
            isStreaming={isStreaming}
          />
        </div>
      </div>
    </div>
  );
}