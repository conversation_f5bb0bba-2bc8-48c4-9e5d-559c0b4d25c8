'use client';

import { Terminal } from '@/components/Terminal';
import { AgentPanel } from '@/components/AgentPanel';
import { CommandPalette } from '@/components/CommandPalette';
import { DebugPanel } from '@/components/DebugPanel';
import { useState, useEffect } from 'react';
import { useAgent } from '@/hooks/useAgent';

interface FileHandle {
  handleId: string;
  absolutePath: string;
  filename: string;
  size?: number;
  createdAt: Date;
  lastAccessed: Date;
}

export default function TradingTerminal() {
  const [fileHandles, setFileHandles] = useState<FileHandle[]>([]);
  const [showCommandPalette, setShowCommandPalette] = useState(false);
  const [showAgentPanel, setShowAgentPanel] = useState(true);
  const { socket } = useAgent();

  // Global keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + K for command palette
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setShowCommandPalette(true);
      }
      // Cmd/Ctrl + \ to toggle agent panel
      if ((e.metaKey || e.ctrlKey) && e.key === '\\') {
        e.preventDefault();
        setShowAgentPanel(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  const handleCommandSelect = (command: string) => {
    // This will be handled by the Terminal component
    const event = new CustomEvent('terminal-command', { detail: command });
    window.dispatchEvent(event);
  };

  return (
    <div className="min-h-screen bg-black">
      <div className="h-screen flex flex-col">
        {/* Minimal Header */}
        <div className="bg-black border-b border-neutral-900 px-4 py-2 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-white font-semibold text-sm tracking-tight">TraderCLI</h1>
            <div className="flex items-center space-x-2 text-xs text-neutral-600">
              <span>⌘K</span>
              <span className="text-neutral-700">•</span>
              <span>⌘\\</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowAgentPanel(!showAgentPanel)}
              className={`p-1 rounded transition-colors ${
                showAgentPanel ? 'text-white' : 'text-neutral-700'
              }`}
              title="Toggle Agent Panel"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                  d="M9 17V7m0 10l-3-3m3 3l3-3m5 3V7m0 10l-3-3m3 3l3-3" />
              </svg>
            </button>
          </div>
        </div>
        
        {/* Main Layout */}
        <div className="flex-1 flex">
          {/* Terminal */}
          <div className="flex-1 p-4">
            <div className="h-full rounded-2xl overflow-hidden shadow-2xl">
              <Terminal fileHandles={fileHandles} onFileHandlesUpdate={setFileHandles} />
            </div>
          </div>
          
          {/* Agent Panel */}
          {showAgentPanel && (
            <div className="w-1/2 p-4 pl-2 animate-in slide-in-from-right duration-300">
              <div className="h-full bg-black rounded-2xl shadow-2xl overflow-hidden border border-neutral-900">
                <AgentPanel socket={socket} />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Command Palette */}
      <CommandPalette
        isOpen={showCommandPalette}
        onClose={() => setShowCommandPalette(false)}
        onSelectCommand={handleCommandSelect}
      />
      
      {/* Debug Panel - Commented out for production */}
      {/* <DebugPanel socket={socket} /> */}
    </div>
  );
}