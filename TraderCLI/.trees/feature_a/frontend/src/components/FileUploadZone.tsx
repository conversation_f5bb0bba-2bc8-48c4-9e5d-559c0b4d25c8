'use client';

import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';

interface FileUploadZoneProps {
  onFileUpload: (file: File) => void;
  isActive?: boolean;
}

export function FileUploadZone({ onFileUpload, isActive = false }: FileUploadZoneProps) {
  const onDrop = useCallback((acceptedFiles: File[]) => {
    acceptedFiles.forEach(file => {
      onFileUpload(file);
    });
  }, [onFileUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/json': ['.json'],
      'text/plain': ['.txt']
    },
    multiple: true
  });

  if (!isActive) return null;

  return (
    <div
      {...getRootProps()}
      className={`
        border-2 border-dashed rounded-lg p-8 mb-4 text-center cursor-pointer
        transition-colors duration-200
        ${isDragActive 
          ? 'border-blue-500 bg-blue-500 bg-opacity-10' 
          : 'border-gray-600 hover:border-gray-500'
        }
      `}
    >
      <input {...getInputProps()} />
      {isDragActive ? (
        <div>
          <p className="text-blue-400 text-sm font-bold">Drop files here...</p>
          <p className="text-gray-500 text-xs mt-1">CSV, JSON, or TXT files</p>
        </div>
      ) : (
        <div>
          <p className="text-gray-400 text-sm">
            Drag & drop trade data files here
          </p>
          <p className="text-gray-500 text-xs mt-1">
            or click to select CSV, JSON, or TXT files
          </p>
        </div>
      )}
    </div>
  );
}