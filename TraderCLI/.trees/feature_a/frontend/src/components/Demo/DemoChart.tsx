'use client';

import { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';

interface ChartData {
  time: string;
  value: number;
}

interface DemoChartProps {
  data: ChartData[];
  height?: number;
}

export function DemoChart({ data, height = 400 }: DemoChartProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<any>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    // For demo, we'll use a canvas-based chart simulation
    // In production, this would use TradingView Lightweight Charts
    if (!chartContainerRef.current || !mounted || data.length === 0) return;

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const container = chartContainerRef.current;
    container.innerHTML = '';
    container.appendChild(canvas);

    // Set canvas size
    canvas.width = container.clientWidth;
    canvas.height = height;

    // Draw chart
    const padding = 40;
    const chartWidth = canvas.width - padding * 2;
    const chartHeight = canvas.height - padding * 2;

    // Clear canvas
    ctx.fillStyle = '#111827';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Find min/max values
    const values = data.map(d => d.value);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);
    const valueRange = maxValue - minValue;

    // Draw grid lines
    ctx.strokeStyle = '#1f2937';
    ctx.lineWidth = 1;
    
    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = padding + (chartHeight / 5) * i;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(canvas.width - padding, y);
      ctx.stroke();
      
      // Value labels
      ctx.fillStyle = '#9ca3af';
      ctx.font = '12px monospace';
      const value = maxValue - (valueRange / 5) * i;
      ctx.fillText(`$${value.toFixed(0)}`, 5, y + 4);
    }

    // Draw the line chart
    ctx.strokeStyle = '#3b82f6';
    ctx.lineWidth = 2;
    ctx.beginPath();

    data.forEach((point, index) => {
      const x = padding + (chartWidth / (data.length - 1)) * index;
      const y = padding + chartHeight - ((point.value - minValue) / valueRange) * chartHeight;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();

    // Draw area under the line
    ctx.fillStyle = 'rgba(59, 130, 246, 0.1)';
    ctx.lineTo(canvas.width - padding, canvas.height - padding);
    ctx.lineTo(padding, canvas.height - padding);
    ctx.closePath();
    ctx.fill();

    // Draw data points
    ctx.fillStyle = '#3b82f6';
    data.forEach((point, index) => {
      if (index % 10 === 0) { // Only show some points
        const x = padding + (chartWidth / (data.length - 1)) * index;
        const y = padding + chartHeight - ((point.value - minValue) / valueRange) * chartHeight;
        
        ctx.beginPath();
        ctx.arc(x, y, 3, 0, Math.PI * 2);
        ctx.fill();
      }
    });

    // Current value indicator
    const lastPoint = data[data.length - 1];
    const lastX = canvas.width - padding;
    const lastY = padding + chartHeight - ((lastPoint.value - minValue) / valueRange) * chartHeight;
    
    // Draw current price line
    ctx.strokeStyle = '#10b981';
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(padding, lastY);
    ctx.lineTo(canvas.width - padding, lastY);
    ctx.stroke();
    ctx.setLineDash([]);

    // Current price label
    ctx.fillStyle = '#10b981';
    ctx.fillRect(canvas.width - padding + 5, lastY - 12, 70, 24);
    ctx.fillStyle = '#ffffff';
    ctx.font = '12px monospace';
    ctx.fillText(`$${lastPoint.value.toFixed(2)}`, canvas.width - padding + 10, lastY + 2);

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current) {
        canvas.width = chartContainerRef.current.clientWidth;
        // Redraw chart on resize
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [data, height, mounted]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="bg-gray-900 rounded-lg p-4 border border-gray-800"
    >
      <div ref={chartContainerRef} style={{ height }} />
      <div className="mt-4 flex items-center justify-between text-sm text-gray-400">
        <span>90-Day Performance</span>
        <div className="flex items-center space-x-4">
          <span className="flex items-center">
            <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
            Current: ${data[data.length - 1]?.value?.toFixed(2) || '0.00'}
          </span>
          <span className="flex items-center">
            <span className="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
            Start: ${data[0]?.value?.toFixed(2) || '0.00'}
          </span>
        </div>
      </div>
    </motion.div>
  );
}