'use client';

import { motion } from 'framer-motion';

interface Metric {
  winRate: number;
  profitFactor: number;
  sharpeRatio: number;
  totalPnL: number;
}

interface DemoMetricsProps {
  metrics: Metric;
}

export function DemoMetrics({ metrics }: DemoMetricsProps) {
  const formatValue = (value: number, decimals = 2) => {
    return Math.abs(value).toFixed(decimals);
  };

  const getChangeColor = (value: number) => {
    return value >= 0 ? 'text-green-400' : 'text-red-400';
  };

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
      {/* Win Rate */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-gray-900 rounded-lg p-4 border border-gray-800"
      >
        <div className="flex items-center justify-between mb-1">
          <h3 className="text-xs font-medium text-gray-500">Win Rate</h3>
          <motion.span 
            key={metrics.winRate}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-xs text-green-400"
          >
            {metrics.winRate > 67.5 ? '↑' : '↓'}
          </motion.span>
        </div>
        <div className="flex items-baseline space-x-1">
          <motion.span 
            key={`wr-${metrics.winRate.toFixed(1)}`}
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            className="text-xl font-bold"
          >
            {metrics.winRate.toFixed(1)}
          </motion.span>
          <span className="text-xs text-gray-500">%</span>
        </div>
        <div className="mt-2 h-6">
          <svg className="w-full h-full">
            <polyline
              fill="none"
              stroke="#10b981"
              strokeWidth="1.5"
              points={`0,15 25,12 50,10 75,8 100,${15 - (metrics.winRate - 60) * 0.5}`}
            />
          </svg>
        </div>
      </motion.div>

      {/* Profit Factor */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.05 }}
        className="bg-gray-900 rounded-lg p-4 border border-gray-800"
      >
        <div className="flex items-center justify-between mb-1">
          <h3 className="text-xs font-medium text-gray-500">Profit Factor</h3>
          <motion.span 
            key={metrics.profitFactor}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className={`text-xs ${metrics.profitFactor > 2 ? 'text-green-400' : 'text-yellow-400'}`}
          >
            {metrics.profitFactor > 2.34 ? '↑' : '↓'}
          </motion.span>
        </div>
        <div className="flex items-baseline">
          <motion.span 
            key={`pf-${metrics.profitFactor.toFixed(2)}`}
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            className="text-xl font-bold"
          >
            {metrics.profitFactor.toFixed(2)}
          </motion.span>
        </div>
        <div className="mt-2 h-6">
          <svg className="w-full h-full">
            <polyline
              fill="none"
              stroke={metrics.profitFactor > 2 ? '#10b981' : '#eab308'}
              strokeWidth="1.5"
              points={`0,15 25,13 50,11 75,9 100,${20 - metrics.profitFactor * 5}`}
            />
          </svg>
        </div>
      </motion.div>

      {/* Sharpe Ratio */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.1 }}
        className="bg-gray-900 rounded-lg p-4 border border-gray-800"
      >
        <div className="flex items-center justify-between mb-1">
          <h3 className="text-xs font-medium text-gray-500">Sharpe</h3>
          <motion.span 
            key={metrics.sharpeRatio}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className={`text-xs ${metrics.sharpeRatio > 1.5 ? 'text-green-400' : 'text-yellow-400'}`}
          >
            {metrics.sharpeRatio > 1.89 ? '↑' : '↓'}
          </motion.span>
        </div>
        <div className="flex items-baseline">
          <motion.span 
            key={`sr-${metrics.sharpeRatio.toFixed(2)}`}
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            className="text-xl font-bold"
          >
            {metrics.sharpeRatio.toFixed(2)}
          </motion.span>
        </div>
        <div className="mt-2 h-6">
          <svg className="w-full h-full">
            <polyline
              fill="none"
              stroke={metrics.sharpeRatio > 1.5 ? '#10b981' : '#eab308'}
              strokeWidth="1.5"
              points={`0,15 25,14 50,13 75,12 100,${20 - metrics.sharpeRatio * 6}`}
            />
          </svg>
        </div>
      </motion.div>

      {/* Total P&L */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.15 }}
        className="bg-gray-900 rounded-lg p-4 border border-gray-800"
      >
        <div className="flex items-center justify-between mb-1">
          <h3 className="text-xs font-medium text-gray-500">Total P&L</h3>
          <motion.span 
            key={metrics.totalPnL}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className={`text-xs ${getChangeColor(metrics.totalPnL)}`}
          >
            {metrics.totalPnL >= 12450 ? '↑' : '↓'}
          </motion.span>
        </div>
        <div className="flex items-baseline">
          <motion.span 
            key={`pnl-${Math.floor(metrics.totalPnL)}`}
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            className={`text-xl font-bold ${getChangeColor(metrics.totalPnL)}`}
          >
            ${formatValue(metrics.totalPnL / 1000, 1)}k
          </motion.span>
        </div>
        <div className="mt-2 h-6">
          <svg className="w-full h-full">
            <polyline
              fill="none"
              stroke={metrics.totalPnL >= 12450 ? "#10b981" : "#ef4444"}
              strokeWidth="1.5"
              points={`0,15 25,13 50,11 75,9 100,${15 - (metrics.totalPnL - 12000) / 200}`}
            />
          </svg>
        </div>
      </motion.div>
    </div>
  );
}