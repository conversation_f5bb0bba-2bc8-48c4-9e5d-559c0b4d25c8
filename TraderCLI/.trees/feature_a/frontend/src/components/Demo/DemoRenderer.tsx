'use client';

import { AgentMessage } from '@/types/agent';
import { motion } from 'framer-motion';

interface DemoRendererProps {
  message: AgentMessage;
}

export function DemoRenderer({ message }: DemoRendererProps) {
  const isUser = message.content.text?.startsWith('Show') || 
                 message.content.text?.startsWith('Analyze') ||
                 message.content.text?.includes('?');

  switch (message.type) {
    case 'text':
      return (
        <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
          <div className={`max-w-[80%] ${isUser ? 'bg-blue-600' : 'bg-gray-800'} rounded-lg px-4 py-2`}>
            <p className="text-sm">{message.content.text}</p>
          </div>
        </div>
      );

    case 'metrics':
      const metrics = message.content.metrics;
      if (!metrics) return null;
      
      return (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-gray-800 rounded-lg p-4"
        >
          <p className="text-xs text-gray-400 mb-3">Performance Metrics</p>
          <div className="grid grid-cols-2 gap-3">
            {metrics.cards.map((card, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="bg-gray-900 rounded p-3 border border-gray-700"
              >
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-gray-400">{card.title}</span>
                  {card.change && (
                    <span className={`text-xs ${
                      card.change.direction === 'up' ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {card.change.direction === 'up' ? '↑' : '↓'} {card.change.percentage}%
                    </span>
                  )}
                </div>
                <div className="text-lg font-bold">
                  {card.value}{card.unit || ''}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      );

    case 'chart':
      return (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3 }}
          className="bg-gray-800 rounded-lg p-4"
        >
          <p className="text-xs text-gray-400 mb-3">Equity Curve Analysis</p>
          <div className="h-48 bg-gray-900 rounded flex items-center justify-center">
            <div className="text-center">
              <svg className="w-16 h-16 mx-auto mb-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <p className="text-sm text-gray-400">Interactive Chart</p>
              <p className="text-xs text-gray-500 mt-1">Full chart rendered with TradingView</p>
            </div>
          </div>
        </motion.div>
      );

    case 'artifact':
      const artifact = message.content.artifact;
      if (!artifact) return null;

      return (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="bg-gray-800 rounded-lg overflow-hidden"
        >
          <div className="bg-gray-900 px-4 py-2 flex items-center justify-between">
            <span className="text-xs text-gray-400">Analysis Report</span>
            <span className="text-xs text-blue-400">View Full</span>
          </div>
          <div className="p-4 max-h-96 overflow-y-auto">
            <div className="prose prose-invert prose-sm max-w-none">
              {artifact.content.split('\n').map((line, index) => {
                if (line.startsWith('###')) {
                  return <h3 key={index} className="text-sm font-semibold mt-4 mb-2 text-blue-400">{line.replace('###', '')}</h3>;
                } else if (line.startsWith('##')) {
                  return <h2 key={index} className="text-base font-bold mt-4 mb-2">{line.replace('##', '')}</h2>;
                } else if (line.startsWith('-')) {
                  return <li key={index} className="text-sm text-gray-300 ml-4">{line.replace('-', '')}</li>;
                } else if (line.trim()) {
                  return <p key={index} className="text-sm text-gray-300 mb-2">{line}</p>;
                }
                return null;
              })}
            </div>
          </div>
        </motion.div>
      );

    default:
      return null;
  }
}