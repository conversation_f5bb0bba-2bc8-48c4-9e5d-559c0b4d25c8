'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { TerminalOutput } from './TerminalOutput';
import { TerminalInput } from './TerminalInput';
import { FileUploadZone } from './FileUploadZone';
import { useAgent } from '@/hooks/useAgent';
import { ConnectionStatus } from './ConnectionStatus';
import { useDropzone } from 'react-dropzone';

export interface TerminalLine {
  type: 'user' | 'agent' | 'agent-stream' | 'system' | 'error';
  content: string;
  timestamp: Date;
}

interface FileHandle {
  handleId: string;
  absolutePath: string;
  filename: string;
  size?: number;
  createdAt: Date;
  lastAccessed: Date;
}

interface TerminalProps {
  fileHandles: FileHandle[];
  onFileHandlesUpdate: (handles: FileHandle[]) => void;
}

export function Terminal({ fileHandles, onFileHandlesUpdate }: TerminalProps) {
  const [history, setHistory] = useState<TerminalLine[]>([
    {
      type: 'system',
      content: 'Trading Agent v1.0.0 - Connected to Gemini 2.5 Pro',
      timestamp: new Date()
    },
    {
      type: 'system', 
      content: 'Type "help" for available commands or just chat naturally',
      timestamp: new Date()
    }
  ]);
  const [showUploadZone, setShowUploadZone] = useState(false);
  
  const { sendMessage, isProcessing, connectionStatus, fileHandles: agentFileHandles, socket } = useAgent();
  const terminalRef = useRef<HTMLDivElement>(null);
  
  // Update file handles when agent provides them
  useEffect(() => {
    if (agentFileHandles.length > 0) {
      onFileHandlesUpdate(agentFileHandles);
    }
  }, [agentFileHandles, onFileHandlesUpdate]);
  
  useEffect(() => {
    // Add connection status message when status changes
    if (connectionStatus === 'connected') {
      setHistory(prev => [...prev, {
        type: 'system',
        content: '✅ Connected to backend on port 8080',
        timestamp: new Date()
      }]);
    } else if (connectionStatus === 'error') {
      setHistory(prev => [...prev, {
        type: 'error',
        content: '❌ Connection failed. Please check if backend is running on port 8080',
        timestamp: new Date()
      }]);
    }
  }, [connectionStatus]);

  const handleCommand = async (input: string) => {
    // Add user input to history
    setHistory(prev => [...prev, {
      type: 'user',
      content: input,
      timestamp: new Date()
    }]);

    // Special commands
    if (input === 'clear') {
      setHistory([]);
      return;
    }
    
    if (input === 'upload') {
      setShowUploadZone(true);
      setHistory(prev => [...prev, {
        type: 'system',
        content: '📁 File upload zone activated. Drag and drop your trade data files.',
        timestamp: new Date()
      }]);
      return;
    }

    // Send to agent backend
    try {
      await sendMessage(input, {
        onStream: (chunk) => {
          setHistory(prev => {
            const last = prev[prev.length - 1];
            if (last?.type === 'agent-stream') {
              return [
                ...prev.slice(0, -1),
                { ...last, content: last.content + chunk }
              ];
            }
            return [...prev, {
              type: 'agent-stream',
              content: chunk,
              timestamp: new Date()
            }];
          });
        }
      });
    } catch (error) {
      setHistory(prev => [...prev, {
        type: 'error',
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date()
      }]);
    }
  };

  // Auto-scroll to bottom
  useEffect(() => {
    terminalRef.current?.scrollTo(0, terminalRef.current.scrollHeight);
  }, [history]);

  // Handle file upload
  const handleFileUpload = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = async (e) => {
      const content = e.target?.result as string;
      
      // Send special command to agent to process uploaded file
      // Send file to backend via socket
      if (socket) {
        socket.emit('file-upload', { filename: file.name, content });
      }
      
      // Tell the agent about the uploaded file
      await sendMessage(`I've uploaded a file named "${file.name}". It has been saved with a file handle. Please analyze it.`, {
        onStream: (chunk) => {
          setHistory(prev => {
            const last = prev[prev.length - 1];
            if (last?.type === 'agent-stream') {
              return [
                ...prev.slice(0, -1),
                { ...last, content: last.content + chunk }
              ];
            }
            return [...prev, {
              type: 'agent-stream',
              content: chunk,
              timestamp: new Date()
            }];
          });
        }
      });
      
      // Add upload notification to history
      setHistory(prev => [...prev, {
        type: 'system',
        content: `📤 Uploading ${file.name}...`,
        timestamp: new Date()
      }]);
      
      // Hide upload zone after file is selected
      setShowUploadZone(false);
    };
    reader.readAsText(file);
  }, [socket, sendMessage]);

  // Setup dropzone for the entire terminal area
  const onDrop = useCallback((acceptedFiles: File[]) => {
    acceptedFiles.forEach(file => {
      handleFileUpload(file);
    });
  }, [handleFileUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/json': ['.json'],
      'text/plain': ['.txt']
    },
    multiple: true,
    noClick: true, // Don't trigger on click since we have the terminal input
    noKeyboard: true
  });

  return (
    <div 
      className="flex-1 bg-gray-950 text-gray-100 font-mono text-sm overflow-hidden flex flex-col relative"
      {...getRootProps()}
    >
      <input {...getInputProps()} />
      {isDragActive && (
        <div className="absolute inset-0 bg-blue-500 bg-opacity-20 z-50 flex items-center justify-center">
          <div className="bg-gray-900 border-2 border-blue-500 rounded-lg p-8 text-center">
            <p className="text-blue-400 text-lg font-bold">Drop files here...</p>
            <p className="text-gray-400 text-sm mt-2">CSV, JSON, or TXT files</p>
          </div>
        </div>
      )}
      <ConnectionStatus status={connectionStatus} />
      {/* Output Area */}
      <div ref={terminalRef} className="flex-1 overflow-y-auto p-4 space-y-2">
        {/* File Upload Zone */}
        <FileUploadZone 
          isActive={showUploadZone} 
          onFileUpload={handleFileUpload}
        />
        
        {history.map((line, i) => (
          <TerminalOutput key={i} line={line} />
        ))}
        {isProcessing && (
          <div className="flex items-center space-x-2 text-blue-400">
            <span className="animate-pulse">◼</span>
            <span>Processing...</span>
          </div>
        )}
      </div>
      
      {/* Input Area */}
      <TerminalInput onSubmit={handleCommand} disabled={isProcessing} />
    </div>
  );
}