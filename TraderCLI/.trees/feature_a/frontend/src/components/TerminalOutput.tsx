import { TerminalLine } from './Terminal';

export function TerminalOutput({ line }: { line: TerminalLine }) {
  const getPrefix = () => {
    switch (line.type) {
      case 'user':
        return <span className="text-green-400">you@trader</span>;
      case 'agent':
      case 'agent-stream':
        return <span className="text-blue-400">agent</span>;
      case 'system':
        return <span className="text-yellow-400">system</span>;
      case 'error':
        return <span className="text-red-400">error</span>;
    }
  };

  return (
    <div className="group">
      <div className="flex items-start space-x-2">
        <span className="text-gray-500">{getPrefix()}:</span>
        <div className="flex-1 whitespace-pre-wrap">
          {line.content}
        </div>
      </div>
    </div>
  );
}