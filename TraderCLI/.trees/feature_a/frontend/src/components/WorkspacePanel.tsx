'use client';

import React from 'react';
import { Box, Text } from '@/components/ui';

interface FileHandle {
  handleId: string;
  absolutePath: string;
  filename: string;
  size?: number;
  createdAt: Date;
  lastAccessed: Date;
}

interface WorkspacePanelProps {
  handles: FileHandle[];
  onHandleClick?: (handle: FileHandle) => void;
}

export function WorkspacePanel({ handles, onHandleClick }: WorkspacePanelProps) {
  return (
    <Box
      borderStyle="round"
      borderColor="blue"
      paddingX={1}
      paddingY={0}
      flexDirection="column"
      minHeight={10}
    >
      <Text bold color="blue">
        📁 Workspace (Active Files)
      </Text>
      <Text dimColor>────────────────────────</Text>
      
      {handles.length === 0 ? (
        <Text dimColor italic>No active files</Text>
      ) : (
        handles.map((handle) => (
          <Box key={handle.handleId} flexDirection="row" gap={1}>
            <Text color="cyan" bold>{handle.handleId}:</Text>
            <Text 
              color="white"
              underline={!!onHandleClick}
              onClick={() => onHandleClick?.(handle)}
            >
              {handle.filename}
            </Text>
            {handle.size !== undefined && (
              <Text dimColor>({formatFileSize(handle.size)})</Text>
            )}
          </Box>
        ))
      )}
    </Box>
  );
}

function formatFileSize(bytes: number): string {
  if (bytes < 1024) return `${bytes}B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
  return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
}