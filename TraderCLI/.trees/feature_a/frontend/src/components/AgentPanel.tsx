'use client';

import { useState, useEffect } from 'react';
import { Socket } from 'socket.io-client';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

export type AgentState = 
  | 'idle'
  | 'thinking'
  | 'searching'
  | 'reading_files'
  | 'writing_code'
  | 'running_command'
  | 'analyzing_data'
  | 'error';

interface AgentActivity {
  state: AgentState;
  description: string;
  details?: string;
  timestamp: Date;
}

interface Artifact {
  id: string;
  type: 'code' | 'visualization' | 'terminal' | 'data';
  title: string;
  content: string;
  language?: string;
  url?: string;
  timestamp: Date;
  path?: string;
  isStreaming?: boolean;
}

interface AgentPanelProps {
  socket: Socket | null;
}

const stateConfig: Record<AgentState, { label: string; color: string; dotColor: string }> = {
  idle: { 
    label: 'Idle',
    color: 'text-neutral-500',
    dotColor: 'bg-neutral-500',
  },
  thinking: { 
    label: 'Processing',
    color: 'text-indigo-400',
    dotColor: 'bg-indigo-400',
  },
  searching: { 
    label: 'Searching',
    color: 'text-sky-400',
    dotColor: 'bg-sky-400',
  },
  reading_files: { 
    label: 'Reading',
    color: 'text-teal-400',
    dotColor: 'bg-teal-400',
  },
  writing_code: { 
    label: 'Writing',
    color: 'text-yellow-400',
    dotColor: 'bg-yellow-400',
  },
  running_command: { 
    label: 'Executing',
    color: 'text-orange-400',
    dotColor: 'bg-orange-400',
  },
  analyzing_data: { 
    label: 'Analyzing',
    color: 'text-violet-400',
    dotColor: 'bg-violet-400',
  },
  error: { 
    label: 'Error',
    color: 'text-red-400',
    dotColor: 'bg-red-400',
  },
};

export function AgentPanel({ socket }: AgentPanelProps) {
  const [currentState, setCurrentState] = useState<AgentState>('idle');
  const [currentActivity, setCurrentActivity] = useState<AgentActivity | null>(null);
  const [recentActivities, setRecentActivities] = useState<AgentActivity[]>([]);
  const [artifacts, setArtifacts] = useState<Artifact[]>([]);
  const [activeTab, setActiveTab] = useState<string | null>(null);

  useEffect(() => {
    console.log('[AgentPanel] Socket prop received:', !!socket, socket?.id);
    if (!socket) {
      console.warn('[AgentPanel] No socket provided!');
      return;
    }

    // Listen for agent state updates
    socket.on('agent-state', (data: { state: string; description: string; details?: string }) => {
      console.log('[AgentPanel] Received agent-state:', data);
      
      // Map incoming state strings to valid AgentState values
      const stateMapping: Record<string, AgentState> = {
        'idle': 'idle',
        'thinking': 'thinking',
        'searching': 'searching',
        'reading_files': 'reading_files',
        'writing_code': 'writing_code',
        'running_command': 'running_command',
        'analyzing_data': 'analyzing_data',
        'error': 'error',
        // Handle any variations or additional states from backend
        'reading': 'reading_files',
        'writing': 'writing_code',
        'executing': 'running_command',
        'analyzing': 'analyzing_data'
      };
      
      // Get valid state or default to 'thinking'
      const validState = stateMapping[data.state] || 'thinking';
      console.log('[AgentPanel] Mapped state:', data.state, '->', validState);
      
      const activity: AgentActivity = {
        state: validState,
        description: data.description,
        details: data.details,
        timestamp: new Date(),
      };

      setCurrentState(validState);
      setCurrentActivity(activity);
      setRecentActivities(prev => [activity, ...prev.slice(0, 4)]);
    });

    // Listen for tool_call events
    socket.on('tool_call', (data: { tool: string; args: any; timestamp: Date }) => {
      console.log('[AgentPanel] Received tool_call:', data);
      
      // Map agentcli tool names to agent states
      const toolStates: Record<string, AgentState> = {
        // File operations
        'read_file': 'reading_files',
        'write_file': 'writing_code',
        'replace': 'writing_code',
        'list_directory': 'reading_files',
        'read_many_files': 'reading_files',
        
        // Search operations
        'search_file_content': 'searching',
        'glob': 'searching',
        'google_web_search': 'searching',
        
        // Command execution
        'run_shell_command': 'running_command',
        
        // Web operations
        'web_fetch': 'searching',
        
        // Memory operations
        'save_memory': 'thinking',
        
        // Data analysis
        'analyze_data': 'analyzing_data',
      };
      
      const state = toolStates[data.tool] || 'thinking';
      const activity: AgentActivity = {
        state,
        description: `Using ${data.tool}`,
        details: JSON.stringify(data.args).substring(0, 100),
        timestamp: new Date(data.timestamp),
      };
      
      setCurrentState(state);
      setCurrentActivity(activity);
      setRecentActivities(prev => [activity, ...prev.slice(0, 4)]);
    });

    // Listen for new artifacts
    socket.on('artifact', (artifact: Artifact & { isStreaming?: boolean }) => {
      setArtifacts(prev => {
        // If it's a streaming artifact, update existing one
        if (artifact.isStreaming) {
          const existingIndex = prev.findIndex(a => a.id === artifact.id);
          if (existingIndex >= 0) {
            // Update existing artifact
            const updated = [...prev];
            updated[existingIndex] = { ...artifact, timestamp: updated[existingIndex].timestamp };
            return updated;
          }
        }
        
        // Add new artifact
        const newArtifacts = [...prev, { ...artifact, timestamp: new Date() }];
        if (!activeTab && newArtifacts.length === 1) {
          setActiveTab(artifact.id);
        }
        return newArtifacts;
      });
      
      // Auto-switch to new non-streaming artifacts
      if (!artifact.isStreaming) {
        setActiveTab(artifact.id);
      }
    });

    // Listen for terminal output
    socket.on('terminal-output', (data: { command: string; output: string }) => {
      const terminalArtifact: Artifact = {
        id: `terminal-${Date.now()}`,
        type: 'terminal',
        title: data.command,
        content: data.output,
        timestamp: new Date(),
      };
      
      setArtifacts(prev => [...prev, terminalArtifact]);
      setActiveTab(terminalArtifact.id);
    });

    // Listen for tool_result events
    socket.on('tool_result', (data: { tool: string; output: any; error?: any; timestamp: Date }) => {
      console.log('[AgentPanel] Received tool_result:', data);
      
      if (data.tool === 'run_shell_command' || data.tool === 'execute_command') {
        const terminalArtifact: Artifact = {
          id: `terminal-${Date.now()}`,
          type: 'terminal',
          title: `${data.tool}`,
          content: typeof data.output === 'string' ? data.output : JSON.stringify(data.output, null, 2),
          timestamp: new Date(data.timestamp),
        };
        
        setArtifacts(prev => [...prev, terminalArtifact]);
        setActiveTab(terminalArtifact.id);
      }
    });

    return () => {
      socket.off('agent-state');
      socket.off('tool_call');
      socket.off('tool_result');
      socket.off('artifact');
      socket.off('terminal-output');
    };
  }, [socket, activeTab]);

  const config = stateConfig[currentState];
  const activeArtifact = artifacts.find(a => a.id === activeTab);

  return (
    <div className="h-full flex flex-col bg-black rounded-2xl overflow-hidden font-mono">
      {/* Ultra-thin header */}
      <div className="px-6 py-4 border-b border-neutral-800">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Minimal status dot */}
            <div className="relative">
              <div className={`w-2 h-2 rounded-full ${config.dotColor}`}>
                {currentState !== 'idle' && (
                  <div className={`absolute inset-0 rounded-full ${config.dotColor} animate-ping`} />
                )}
              </div>
            </div>
            
            {/* Status text */}
            <div className="flex items-baseline gap-3">
              <span className="text-white text-sm font-medium">
                {currentActivity?.description || 'System Ready'}
              </span>
              <span className={`text-xs ${config.color}`}>
                {config.label}
              </span>
            </div>
          </div>

          {/* Activity indicators */}
          {recentActivities.length > 0 && (
            <div className="flex gap-1.5">
              {recentActivities.slice(0, 3).map((activity, idx) => {
                const actConfig = stateConfig[activity.state];
                return (
                  <div 
                    key={idx}
                    className={`w-1 h-4 rounded-full ${actConfig.dotColor}`}
                    style={{ opacity: 1 - (idx * 0.3) }}
                  />
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Terminal-style tabs */}
      {artifacts.length > 0 && (
        <div className="flex gap-px bg-neutral-900 px-6 pt-3">
          {artifacts.map(artifact => (
            <div
              key={artifact.id}
              className={`flex items-center px-4 py-2 text-xs font-medium transition-all rounded-t-lg cursor-pointer ${
                activeTab === artifact.id 
                  ? 'bg-black text-white' 
                  : 'bg-neutral-800 text-neutral-400 hover:text-white'
              }`}
              onClick={() => setActiveTab(artifact.id)}
            >
              <span className="flex items-center gap-1.5 flex-1">
                {artifact.type === 'code' && (
                  <span className="text-xs opacity-70">{'<>'}</span>
                )}
                {artifact.type === 'terminal' && (
                  <span className="text-xs opacity-70">$</span>
                )}
                {artifact.title}
                {artifact.language && artifact.language !== 'plaintext' && (
                  <span className="text-xs opacity-50">({artifact.language})</span>
                )}
              </span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setArtifacts(prev => prev.filter(a => a.id !== artifact.id));
                  if (activeTab === artifact.id) {
                    const remaining = artifacts.filter(a => a.id !== artifact.id);
                    setActiveTab(remaining[0]?.id || null);
                  }
                }}
                className="ml-2 opacity-50 hover:opacity-100 px-1"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Content area - pure black terminal style */}
      <div className="flex-1 bg-black overflow-auto">
        {artifacts.length === 0 ? (
          <div className="h-full flex items-center justify-center p-8">
            <div className="text-center">
              <div className="text-neutral-700 text-6xl mb-4 font-light">[ ]</div>
              <p className="text-neutral-500 text-sm">Awaiting output</p>
            </div>
          </div>
        ) : activeArtifact && (
          <div className="p-6">
            {activeArtifact.type === 'code' && (
              <div className="text-sm">
                <SyntaxHighlighter
                  language={activeArtifact.language || 'plaintext'}
                  style={vscDarkPlus}
                  customStyle={{
                    margin: 0,
                    padding: '1.5rem',
                    backgroundColor: 'transparent',
                    fontSize: '0.875rem',
                    lineHeight: '1.5',
                  }}
                  showLineNumbers={true}
                  lineNumberStyle={{
                    color: '#4a5568',
                    paddingRight: '1rem',
                    userSelect: 'none',
                  }}
                >
                  {activeArtifact.content}
                </SyntaxHighlighter>
              </div>
            )}

            {activeArtifact.type === 'terminal' && (
              <div className="font-mono text-sm">
                <div className="text-neutral-500 mb-2">$ {activeArtifact.title}</div>
                <pre className="text-green-400 leading-relaxed whitespace-pre-wrap">
                  {activeArtifact.content}
                </pre>
              </div>
            )}

            {activeArtifact.type === 'visualization' && activeArtifact.url && (
              <iframe 
                src={activeArtifact.url}
                className="w-full h-[400px] rounded border border-neutral-800"
                title={activeArtifact.title}
              />
            )}

            {activeArtifact.type === 'data' && (
              <pre className="text-sm text-neutral-400 font-mono leading-relaxed">
                {activeArtifact.content}
              </pre>
            )}
          </div>
        )}
      </div>

      {/* Ultra-minimal progress indicator */}
      {currentState !== 'idle' && (
        <div className="h-px bg-neutral-900">
          <div 
            className={`h-full ${config.dotColor} opacity-50`}
            style={{
              width: '40%',
              animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            }}
          />
        </div>
      )}
    </div>
  );
}