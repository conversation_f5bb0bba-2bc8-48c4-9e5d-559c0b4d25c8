import { useCallback, useState, useEffect } from 'react';
import { io, Socket } from 'socket.io-client';

// Global singleton socket instance shared across all components
let socket: Socket | null = null;
let initializationPromise: Promise<Socket> | null = null;

interface SendMessageOptions {
  onStream?: (chunk: string) => void;
  onToolUse?: (tool: any) => void;
}

type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

interface FileHandle {
  handleId: string;
  absolutePath: string;
  filename: string;
  size?: number;
  createdAt: Date;
  lastAccessed: Date;
}

export function useAgent() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [fileHandles, setFileHandles] = useState<FileHandle[]>([]);
  
  useEffect(() => {
    // Initialize socket connection only once across all components
    const initSocket = async () => {
      if (!socket && !initializationPromise) {
        setConnectionStatus('connecting');
        
        initializationPromise = new Promise<Socket>((resolve) => {
          const newSocket = io(process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:8080', {
            reconnection: true,
            reconnectionDelay: 1000,
            reconnectionAttempts: 5
          });
          
          newSocket.on('connect', () => {
            console.log('[useAgent] Socket connected:', newSocket.id);
          });
          
          socket = newSocket;
          resolve(newSocket);
        });
      }
      
      const currentSocket = socket || await initializationPromise!;
      
      // Set up event listeners for this component instance
      const handleConnect = () => {
        console.log('[useAgent] Connected event for component');
        setConnectionStatus('connected');
      };
      
      const handleDisconnect = () => {
        console.log('[useAgent] Disconnected event for component');
        setConnectionStatus('disconnected');
      };
      
      const handleError = (error: any) => {
        console.error('[useAgent] Connection error:', error);
        setConnectionStatus('error');
      };
      
      const handleFileHandles = (handles: FileHandle[]) => {
        console.log('[useAgent] Received file handles:', handles);
        setFileHandles(handles);
      };
      
      const handleFileUploaded = (result: any) => {
        console.log('[useAgent] File upload result:', result);
      };
      
      currentSocket.on('connect', handleConnect);
      currentSocket.on('disconnect', handleDisconnect);
      currentSocket.on('connect_error', handleError);
      currentSocket.on('file-handles', handleFileHandles);
      currentSocket.on('file-uploaded', handleFileUploaded);
      
      // Update status based on current socket state
      if (currentSocket.connected) {
        setConnectionStatus('connected');
      }
      
      // Cleanup only removes listeners, doesn't disconnect the socket
      return () => {
        currentSocket.off('connect', handleConnect);
        currentSocket.off('disconnect', handleDisconnect);
        currentSocket.off('connect_error', handleError);
        currentSocket.off('file-handles', handleFileHandles);
        currentSocket.off('file-uploaded', handleFileUploaded);
      };
    };
    
    initSocket();
  }, []);

  const sendMessage = useCallback(async (
    message: string,
    options?: SendMessageOptions
  ) => {
    if (!socket || socket.disconnected) {
      throw new Error('Not connected to backend. Please check if the backend is running.');
    }

    setIsProcessing(true);

    return new Promise((resolve, reject) => {
      socket!.emit('message', { content: message });

      if (options?.onStream) {
        socket!.on('stream', options.onStream);
      }

      if (options?.onToolUse) {
        socket!.on('tool-use', options.onToolUse);
      }

      socket!.once('complete', (result) => {
        socket!.off('stream');
        socket!.off('tool-use');
        setIsProcessing(false);
        resolve(result);
      });

      socket!.once('error', (error) => {
        socket!.off('stream');
        socket!.off('tool-use');
        setIsProcessing(false);
        reject(error);
      });
    });
  }, []);

  return { sendMessage, isProcessing, connectionStatus, fileHandles, socket };
}