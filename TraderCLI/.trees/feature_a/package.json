{"name": "tradercli", "version": "1.0.0", "description": "Trading Performance Analysis Terminal", "scripts": {"dev": "concurrently -n \"agentcli,backend,frontend\" -c \"magenta,yellow,cyan\" \"npm run dev:agentcli\" \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:agentcli": "cd backend/agentcli && GEMINI_API_KEY=${GEMINI_API_KEY:-AIzaSyAAp_f1oQYgOMPVdECTxvW6bpD7w6BOCX4} node packages/cli/dist/index.js --api-mode --api-port 3001 --checkpointing", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "concurrently -n \"backend,frontend\" -c \"yellow,cyan\" \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm start", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules backend/dist frontend/.next", "setup": "npm run install:all"}, "devDependencies": {"concurrently": "^8.2.2"}, "author": "", "license": "ISC"}