# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Production builds
dist/
build/
.next/
out/

# Environment variables
.env
.env.local
.env.production.local
.env.development.local
.env.test.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Logs
logs/
*.log
frontend.log

# Testing
coverage/
.nyc_output/

# Temporary files
.tmp/
tmp/
temp/

# Session data (user-specific)
sessions/
uploads/

# Trading data files (sensitive)
*.csv
!test-data/*.csv

# AgentCLI specific
backend/agentcli/node_modules/
backend/agentcli/dist/
backend/agentcli/packages/*/dist/
backend/agentcli/packages/*/node_modules/
backend/agentcli/.tmp/
backend/agentcli/bundle/

# User data storage
~/.tradercli/
~/.gemini/

# API keys and secrets
*.key
*.pem
secrets/

# Package lock files (keep only root ones)
backend/agentcli/packages/*/package-lock.json

# TypeScript
*.tsbuildinfo

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
.directory

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea/
*.sublime-project
*.sublime-workspace

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/

# Jupyter Notebook
.ipynb_checkpoints

# Raw data files
raw_trade_data.txt

# Checkpoint data (user-specific)
checkpoints/

# Generated documentation
docs/_site/
docs/.jekyll-cache/