#!/bin/bash

# TraderCLI Stop Script
# This script stops all running services including AgentCLI

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Change to the TraderCLI directory
cd "$SCRIPT_DIR"

echo "🛑 Stopping TraderCLI services..."

# Kill processes on ports
echo "   Stopping AgentCLI on port 3001..."
lsof -ti:3001 | xargs kill -9 2>/dev/null

echo "   Stopping Frontend on port 3000..."
lsof -ti:3000 | xargs kill -9 2>/dev/null

echo "   Stopping Backend on port 8080..."
lsof -ti:8080 | xargs kill -9 2>/dev/null

# Also kill any node processes running our apps
echo "   Cleaning up processes..."
pkill -f "agentcli.*--api-mode" 2>/dev/null
pkill -f "next-server" 2>/dev/null
pkill -f "tsx watch" 2>/dev/null
pkill -f "TraderCLI" 2>/dev/null

echo "✅ All services stopped"