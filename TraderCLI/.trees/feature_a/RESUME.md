# TraderCLI Session Persistence

Since AgentCLI's `/restore` command only works in interactive mode, TraderCLI now has its own session persistence system.

## How It Works

1. **Automatic Session Creation**: Every time you start TraderCLI, a new session is created
2. **Auto-Save**: All conversations and file handles are automatically saved to `~/.tradercli/sessions/`
3. **Session Storage**: Each session is saved as a JSON file with:
   - All messages (user + assistant)
   - File handle mappings (@F1 → /path/to/file)
   - Timestamps

## Resuming a Session

### Option 1: Command Line Flag (TODO)
```bash
"/Applications/AI Project /myclaude/TraderCLI/start.sh" --resume
# This will load the most recent session
```

### Option 2: API Endpoints
```bash
# List all sessions
curl http://localhost:8080/api/sessions

# Load a specific session
curl -X POST http://localhost:8080/api/sessions/session-1234567890/load
```

### Option 3: Terminal Command (TODO)
```
Type 'resume' in the terminal to see and load previous sessions
```

## File Upload Feature

Type `upload` in the terminal to activate the file upload zone. You can then drag and drop:
- CSV files with trade data
- JSON files with trading history
- TXT files with raw data

## File Handle System

The agent now understands and uses file handles:
- `@F1`, `@F2`, etc. instead of full paths
- Automatic path resolution
- No more "file not found" errors due to spaces in paths

## Continuous Execution

The agent is now instructed to:
- Complete full workflows without stopping
- Use file handles in commands
- Be proactive in analysis

Start with: `"/Applications/AI Project /myclaude/TraderCLI/start.sh"`