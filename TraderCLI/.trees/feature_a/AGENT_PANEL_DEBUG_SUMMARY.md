# Agent Panel WebSocket Issue - Debug Summary

## Problem Statement
The Agent Panel in the TraderCLI frontend is not updating to show agent activity states (searching, reading files, running commands, etc.) even though the agent is working correctly. The panel remains static showing "System Ready" regardless of what operations the agent is performing.

## Current Architecture

### WebSocket Flow
1. **Frontend (port 3000/3002)** → connects to Backend WebSocket
2. **Backend (port 8080)** → connects to AgentCLI WebSocket  
3. **AgentCLI (port 3001)** → Gemini CLI running in API mode

### Key Files to Investigate

#### 1. Backend Agent Service
**File**: `/Applications/AI Project /myclaude/TraderCLI/backend/src/services/agent.service.ts`
- **Lines 65-152**: Handles WebSocket response from agent<PERSON>li
- **Lines 85-110**: Tool name to state mapping
- **Issue**: The tool names might not match what agent<PERSON><PERSON> actually sends

#### 2. Backend Index (WebSocket Server)
**File**: `/Applications/AI Project /myclaude/TraderCLI/backend/src/index.ts`
- **Lines 167-169**: Emits 'agent-state' events to frontend
- **Lines 139-165**: Emits 'tool_call' and 'tool_result' events
- **Note**: Added logging at line 168 to debug state emissions

#### 3. AgentCLI Tool Names
**Location**: `/Applications/AI Project /myclaude/TraderCLI/backend/agentcli/packages/core/src/tools/`

Actual tool names used by agentcli:
- `list_directory` (not `ls`)
- `read_file` 
- `write_file`
- `replace` (for editing)
- `search_file_content` (not `search`)
- `glob` (for finding files)
- `google_web_search`
- `run_shell_command`
- `web_fetch`
- `save_memory`
- `read_many_files`

## Debugging Steps Taken

1. **Added Console Logging**:
   - Backend agent.service.ts: Log when tools are mapped to states
   - Backend index.ts: Log when agent-state events are emitted
   - Frontend AgentPanel.tsx: Log when events are received

2. **Updated Tool Mappings**:
   - Fixed tool names to match agentcli's actual tool names
   - Added proper state mappings for each tool type

3. **Verified WebSocket Connection**:
   - Frontend connects to backend successfully
   - Backend connects to agentcli successfully
   - Events are flowing (visible in debug panel)

## Current Status

### What's Working:
- WebSocket connections are established
- Agent processes commands correctly
- Tool events are being sent from agentcli
- Debug panel shows events are received

### What's Not Working:
- Agent Panel doesn't update visual state
- State changes aren't being properly emitted or processed
- The panel shows "System Ready" constantly

## Next Steps for Debugging

1. **Check agentcli response format**:
   ```bash
   # In backend logs, look for:
   [AgentService] Response received, type: tool
   [AgentService] Tool use: <actual data structure>
   ```

2. **Verify state change emissions**:
   ```bash
   # Look for these log messages:
   [AgentService] Tool X mapped to state: {state, description}
   [AgentService] Calling onStateChange callback
   [Backend] Emitting agent-state: {state, description}
   ```

3. **Test with specific commands**:
   ```bash
   # Commands that should trigger state changes:
   list files              # Should show "reading_files" state
   search for "trading"    # Should show "searching" state
   run ls -la             # Should show "running_command" state
   ```

4. **Check if agentcli sends tool events differently**:
   - The tool event structure might be different than expected
   - Tool names might be nested differently in the response
   - The WebSocket event types might not match

## Potential Root Causes

1. **Event Structure Mismatch**: 
   - agentcli might send tool data in a different format
   - The `data.tool` object structure might be different

2. **State Callback Not Firing**:
   - The `onStateChange` callback might not be called
   - The state emission might happen before the callback is set

3. **Frontend State Mapping**:
   - The state strings might not match between backend and frontend
   - The AgentState type might be too restrictive

## Files to Check First

1. **Backend Logs**: Check what's actually being received from agentcli
2. **Browser Console**: Check for AgentPanel state mapping logs
3. **WebSocket Messages**: Use browser DevTools Network tab to inspect WebSocket frames

## How to Test

1. Start all services:
   ```bash
   cd "/Applications/AI Project /myclaude/TraderCLI"
   ./start.sh
   ```

2. Open browser console and filter for "[AgentPanel]"

3. Run a command that uses tools:
   ```
   show me the package.json file
   ```

4. Check logs in both terminal (backend) and browser console

The issue is likely in how the backend processes tool events from agentcli and emits state changes to the frontend.