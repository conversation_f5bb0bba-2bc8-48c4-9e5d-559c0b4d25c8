# TraderCLI Adaptive UI Demo - Running Instructions

## 🚀 The Demo is Running!

The frontend server is now running in the background on port **3006**.

### Access the demo at:
# 👉 http://localhost:3006/demo

## Server Details
- **Process**: Running in background with nohup
- **Port**: 3006 (auto-selected as 3000 and 3001 were in use)
- **Log file**: `frontend/frontend.log`

## Managing the Server

### Check if it's running:
```bash
lsof -i :3006
```

### View logs:
```bash
tail -f frontend/frontend.log
```

### Stop the server:
```bash
# Find the process ID
ps aux | grep "next dev"

# Kill the process
kill <PID>
```

### Restart if needed:
```bash
cd frontend
npm run dev
```

## Demo Features to Try

1. **Performance Metrics** - Watch them update every 3 seconds
2. **Chat Queries**:
   - "Show me my trading performance"
   - "Analyze my trading patterns"
   - "Why do I lose money?"
3. **Animations** - Smooth transitions and loading states
4. **Responsive Design** - Resize your browser to see mobile layout

Enjoy exploring the adaptive UI framework!