# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

TraderCLI is a terminal-based trading performance analysis tool that addresses the $11.3B trading software market. It provides AI-powered insights to help traders understand why they lose money, targeting the 15 million+ active traders globally who currently pay $15-600/month for inferior solutions.

## Critical Project Objectives (Data-Driven)

Based on market research, TraderCLI must address these validated pain points:

1. **#1 Complaint**: Broker API sync problems - We MUST have bulletproof trade import
2. **Major Gap**: AI insights locked behind expensive tiers ($80-100/month) - We provide at $15-30
3. **User Need**: "Why do I lose money?" - Focus on actionable, specific insights like "You lose 73% of trades after 2PM"
4. **Target Market**: MetaTrader users (10M+) struggling with integration issues

## Architecture

```
/Applications/AI Project /myclaude/TraderCLI/
├── frontend/          # Next.js terminal UI (port 3000)
│   └── src/
│       ├── app/      # App router
│       ├── components/   # Terminal components
│       └── hooks/       # React hooks (useAgent)
├── backend/          # Express + Socket.io API (port 8080)
│   ├── src/
│   │   ├── index.ts     # WebSocket server
│   │   └── services/    # AgentService (agentcli wrapper)
│   └── agentcli/     # Google's Gemini CLI (port 3001)
└── docs/             # Research and market analysis
```

## Key Features

### File Upload (Drag & Drop)
- **Drag files anywhere** in the terminal window to upload CSV/JSON/TXT files
- Type `upload` to show a dedicated upload zone
- Files are automatically assigned handles (@F1, @F2, etc.)
- The AI can use handles instead of full paths: `python3 @F1`

### Session Persistence & Checkpointing
- **Uses agentcli's built-in checkpoint/restore system**
- Automatic Git snapshots before file modifications
- Checkpoints stored in `~/.gemini/tmp/<project_hash>/checkpoints/`
- Commands:
  - `/restore` - List and restore checkpoints
  - `/chat save <tag>` - Save conversation state
  - `/chat resume <tag>` - Load saved conversation
- See `backend/CHECKPOINT_INTEGRATION.md` for full details

## Development Commands

### Full Paths for Scripts

```bash
# Start all services (recommended) - AgentCLI, Backend, Frontend
cd "/Applications/AI Project /myclaude/TraderCLI"
npm run dev

# Stop all services
cd "/Applications/AI Project /myclaude/TraderCLI"
"/Applications/AI Project /myclaude/TraderCLI/stop.sh"

# Alternative: Use shell scripts
"/Applications/AI Project /myclaude/TraderCLI/start.sh"  # Starts all 3 services
"/Applications/AI Project /myclaude/TraderCLI/stop.sh"   # Stops all services and cleans up

# Alternative: Node.js manager
node "/Applications/AI Project /myclaude/TraderCLI/run.js" start
```

### Individual Service Commands

```bash
# Backend only
cd "/Applications/AI Project /myclaude/TraderCLI/backend"
npm run dev  # Runs on http://localhost:8080

# Frontend only  
cd "/Applications/AI Project /myclaude/TraderCLI/frontend"
npm run dev  # Runs on http://localhost:3001

# Install all dependencies
cd "/Applications/AI Project /myclaude/TraderCLI"
npm run setup
```

### Build Commands

```bash
cd "/Applications/AI Project /myclaude/TraderCLI"
npm run build         # Build both
npm run build:backend # Backend only
npm run build:frontend # Frontend only
```

## Testing Requirements

**CRITICAL**: Tests must be REAL and comprehensive. NEVER create mock tests or simplify tests just to pass.

### Testing Philosophy
- Test actual broker integrations, not mocks
- Test with real trading data (CSV/JSON files)
- Test error cases that traders actually experience
- Performance metrics calculations must be mathematically correct
- Use institutional-grade calculation methods

### Test Implementation
```typescript
// GOOD: Real test with actual data validation
describe('TradeImportTool', () => {
  it('should correctly parse MetaTrader CSV with real-world edge cases', async () => {
    const realMT4Data = await fs.readFile('test-data/mt4-export-with-errors.csv');
    const result = await tradeImportTool.execute({ file: realMT4Data, format: 'mt4' });
    
    // Test actual edge cases traders face
    expect(result.trades[0].symbol).toBe('EURUSD'); // Not EUR/USD
    expect(result.errors).toContain('Missing closing price for trade #45');
    expect(result.warnings).toContain('Timezone mismatch detected');
  });
});

// BAD: Oversimplified mock test
it('should import trades', () => {
  const mockData = { trades: [{ symbol: 'TEST' }] };
  expect(importTrades(mockData)).toBeTruthy(); // Too simple!
});
```

## agentcli Integration Plan

The backend will integrate agentcli (Google's Gemini CLI fork) as the AI engine:

1. **Copy Core Packages**: 
   ```bash
   cp -r "/Applications/AI Project /myclaude/agentcli/packages/core" backend/src/core
   cp -r "/Applications/AI Project /myclaude/agentcli/packages/cli/src/tools" backend/src/tools
   ```

2. **Create Trading Tools** extending BaseTool:
   - TradeImportTool (CSV/JSON with broker-specific parsing)
   - PatternAnalysisTool (win/loss patterns, time-based analysis)
   - PsychologyDetectionTool (revenge trading, tilt detection)
   - PerformanceMetricsTool (Sharpe, Sortino, drawdown)

3. **MCP Server for MT4/MT5**: Separate process for broker connections

## Key Technical Decisions

### Performance Metrics (from research)
- Use 252 trading days for annualization
- Risk-free rate: 3-5% for Sharpe calculations
- Libraries: pandas, numpy, ta-lib for accuracy
- All calculations must match institutional standards

### WebSocket Architecture
- Real-time streaming of AI analysis
- Connection status monitoring
- Automatic reconnection with exponential backoff
- CORS configured for ports 3000, 3001, 8080

### Error Handling
- Clear error messages for connection issues
- Specific broker sync error details
- Data validation errors with line numbers
- Never hide errors - traders need transparency

## Market-Driven Features (Priority Order)

1. **Trade Import** (Week 1-2)
   - CSV/JSON upload with smart parsing
   - MT4/MT5 specific format support
   - Error reporting with specific line numbers

2. **Pattern Recognition** (Week 3-4)
   - Time-based performance ("Do I trade better on Fridays?")
   - Symbol-specific analysis
   - No coding required - natural language

3. **Psychological Analysis** (Week 5-6)
   - Revenge trading detection
   - Overtrading patterns
   - Emotional state correlation

## Environment Configuration

```bash
# backend/.env
PORT=8080
FRONTEND_URL=http://localhost:3000,http://localhost:3001
GEMINI_API_KEY=your-key-here

# frontend/.env.local
NEXT_PUBLIC_AGENT_URL=http://localhost:8080
```

## Common Issues and Solutions

### CORS Errors
- Backend accepts connections from ports 3000 and 3001
- Check backend/src/index.ts for CORS configuration
- Hard refresh browser if seeing cached CORS errors

### Port Already in Use
- Frontend auto-selects next available port (3001 if 3000 is taken)
- Check with: `lsof -i :3000` and `lsof -i :8080`
- Kill processes: `npm run stop` or use stop.sh

### WebSocket Connection Failed
- Ensure backend is running first
- Check connection status indicator (top-right of terminal)
- Verify NEXT_PUBLIC_AGENT_URL in frontend/.env.local

## Code Style Requirements

### TypeScript
- NO `any` types - use `unknown` and type narrowing
- Define interfaces for all data structures
- Strict mode enabled

### React
- Functional components only
- No useEffect for "do this when this changes" - use event handlers
- Keep components pure during rendering
- Use WebSocket for real-time updates

### Testing
- Real data, real scenarios, real edge cases
- Test what traders actually experience
- Mathematical accuracy for all calculations
- Never simplify tests to make them pass

## Next Implementation Steps

1. Complete agentcli integration in AgentService
2. Implement TradeImportTool with MT4/MT5 parsing
3. Add real broker data test files
4. Create MCP server for live broker connections
5. Implement pattern recognition with Gemini AI

Remember: Every feature must solve a real trader problem validated by market research. Focus on "Why do I lose money?" - not vanity metrics.