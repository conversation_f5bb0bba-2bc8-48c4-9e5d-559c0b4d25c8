#!/usr/bin/env python3
import json
import sys
import os

# Define protected paths and patterns
PROTECTED_PATHS = [
    "/etc/",
    "/usr/",
    "/bin/",
    "/sbin/",
    ".git/objects/",
    ".env",
    "credentials",
    "secrets",
    "private_key",
    "id_rsa",
]

PROTECTED_PATTERNS = ["prod", "production", "master", "main"]

try:
    input_data = json.load(sys.stdin)

    tool_name = input_data.get("tool_name", "")
    tool_input = input_data.get("tool_input", {})

    # Check file operations
    if tool_name in ["Write", "Edit", "MultiEdit", "Delete"]:
        file_path = tool_input.get("file_path", "")

        # Check protected paths
        for protected in PROTECTED_PATHS:
            if protected in file_path:
                print(
                    f"Blocking operation on protected path: {file_path}",
                    file=sys.stderr,
                )
                print(
                    f"This file appears to be in a protected location ({protected}).",
                    file=sys.stderr,
                )
                print(
                    "Please work with development or staging files instead.",
                    file=sys.stderr,
                )
                sys.exit(2)  # Exit code 2 blocks the operation

        # Check protected patterns
        for pattern in PROTECTED_PATTERNS:
            if pattern.lower() in file_path.lower():
                # Use JSON output for more control
                output = {
                    "decision": "block",
                    "reason": f"This operation would modify a file containing '{pattern}'. For safety, I'm blocking this. If you really need to modify this file, please confirm by saying 'I confirm I want to edit {file_path}' and I'll proceed with caution.",
                }
                print(json.dumps(output))
                sys.exit(0)

    # Check bash commands
    elif tool_name == "Bash":
        command = tool_input.get("command", "")
        dangerous_commands = ["rm -rf /", "dd if=", ":(){ :|:& };:", "mkfs."]

        for dangerous in dangerous_commands:
            if dangerous in command:
                output = {
                    "decision": "block",
                    "reason": f"This command contains potentially dangerous operation '{dangerous}'. Please use a safer alternative or be more specific about what you want to accomplish.",
                }
                print(json.dumps(output))
                sys.exit(0)

    # Allow everything else
    sys.exit(0)

except Exception:
    # On any error, allow the operation (fail open)
    sys.exit(0)
