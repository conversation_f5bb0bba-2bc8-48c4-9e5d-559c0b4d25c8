#!/usr/bin/env python3
import json
import sys
import subprocess
import os
from pathlib import Path


def run_command(cmd, file_path):
    """Run a command and return success status and output"""
    try:
        result = subprocess.run(
            cmd + [file_path], capture_output=True, text=True, timeout=10
        )
        return result.returncode == 0, result.stdout, result.stderr
    except FileNotFoundError:
        return None, f"{cmd[0]} not installed", ""
    except subprocess.TimeoutExpired:
        return False, "", f"{cmd[0]} timed out"


try:
    # Read input from Claude Code
    input_data = json.load(sys.stdin)

    # Check if this is a relevant operation
    tool_name = input_data.get("tool_name", "")
    if tool_name not in ["Write", "Edit", "MultiEdit"]:
        sys.exit(0)

    # Get file path
    file_path = input_data.get("tool_input", {}).get("file_path", "")
    if not file_path.endswith(".py") or not os.path.exists(file_path):
        sys.exit(0)

    # Track all issues found
    issues = []
    fixes_applied = []

    # 1. Format with Black
    success, stdout, stderr = run_command(["black"], file_path)
    if success:
        fixes_applied.append("✓ Formatted with Black")
    elif success is None:
        issues.append("• Install Black: pip install black")

    # 2. Sort imports with isort
    success, stdout, stderr = run_command(["isort"], file_path)
    if success:
        fixes_applied.append("✓ Sorted imports with isort")
    elif success is None:
        issues.append("• Install isort: pip install isort")

    # 3. Check with flake8
    success, stdout, stderr = run_command(["flake8", "--max-line-length=88"], file_path)
    if not success and success is not None:
        issues.append(f"• Flake8 warnings:\n{stdout}")
    elif success is None:
        issues.append("• Install flake8: pip install flake8")

    # 4. Security check with bandit
    success, stdout, stderr = run_command(["bandit", "-f", "json"], file_path)
    if not success and success is not None:
        # Parse bandit output and report security issues
        try:
            import json as json_lib

            bandit_results = json_lib.loads(stdout)
            if bandit_results.get("results"):
                issues.append("• Security issues found by Bandit:")
                for issue in bandit_results["results"]:
                    issues.append(
                        f"  - {issue['issue_text']} (line {issue['line_number']})"
                    )
        except:
            pass

    # Report results
    if fixes_applied:
        for fix in fixes_applied:
            print(fix)

    if issues:
        print("\n⚠️  Quality checks found issues:")
        for issue in issues:
            print(issue)

    # Always exit 0 for PostToolUse - we don't want to break Claude's flow
    sys.exit(0)

except Exception as e:
    # Silent failure - don't interrupt Claude Code
    sys.exit(0)
