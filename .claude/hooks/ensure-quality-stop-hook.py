#!/usr/bin/env python3
import json
import sys
import subprocess
import os
import re
from pathlib import Path


# This function reads <PERSON>'s conversation transcript to understand what happened
def analyze_transcript(transcript_path):
    """
    The transcript contains every interaction in the session.
    We'll scan it to understand what files were modified and what <PERSON> was trying to do.
    """
    modified_files = set()
    task_description = ""

    try:
        with open(transcript_path, "r") as f:
            for line in f:
                # Each line in the transcript is a JSON object
                try:
                    entry = json.loads(line)

                    # Look for file modifications
                    if entry.get("type") == "tool_use" and entry.get("name") in [
                        "Write",
                        "Edit",
                        "MultiEdit",
                    ]:
                        # Extract the file path from the tool input
                        tool_input = entry.get("input", {})
                        file_path = tool_input.get("file_path")
                        if file_path and file_path.endswith(".py"):
                            modified_files.add(file_path)

                    # Capture the original task from user messages
                    if entry.get("type") == "human_message":
                        task_description = entry.get("content", "")[
                            :200
                        ]  # First 200 chars

                except json.JSONDecodeError:
                    continue

    except FileNotFoundError:
        pass

    return modified_files, task_description


# This function runs our quality checks
def run_quality_checks(modified_files):
    """
    Run various quality checks and return a list of issues that need fixing.
    Each issue includes a description and suggested action.
    """
    issues = []

    # Check 1: Run pytest
    print("Running tests...", file=sys.stderr)
    test_result = subprocess.run(["pytest", "-v"], capture_output=True, text=True)

    if test_result.returncode != 0:
        # Parse the test output to find which tests failed
        failed_tests = []
        for line in test_result.stdout.split("\n"):
            if "FAILED" in line:
                failed_tests.append(line.strip())

        issues.append(
            {
                "type": "test_failure",
                "description": f"Found {len(failed_tests)} failing tests",
                "details": test_result.stdout,
                "action": "Fix the failing tests. Run pytest to see the failures, understand why they're failing, and update either the code or the tests as appropriate.",
            }
        )

    # Check 2: Code coverage
    print("Checking code coverage...", file=sys.stderr)
    coverage_result = subprocess.run(
        ["coverage", "run", "-m", "pytest"], capture_output=True, text=True
    )

    if coverage_result.returncode == 0:
        # Get the coverage report
        report_result = subprocess.run(
            ["coverage", "report"], capture_output=True, text=True
        )

        # Parse coverage percentage from the output
        coverage_match = re.search(r"TOTAL\s+\d+\s+\d+\s+(\d+)%", report_result.stdout)
        if coverage_match:
            coverage_percent = int(coverage_match.group(1))
            if coverage_percent < 80:  # Your threshold
                issues.append(
                    {
                        "type": "low_coverage",
                        "description": f"Code coverage is only {coverage_percent}%",
                        "details": report_result.stdout,
                        "action": f"Increase code coverage to at least 80%. Current coverage is {coverage_percent}%. Add tests for uncovered code paths.",
                    }
                )

    # Check 3: Linting issues
    print("Running linter...", file=sys.stderr)
    for file_path in modified_files:
        if os.path.exists(file_path):
            lint_result = subprocess.run(
                ["pylint", file_path, "--errors-only"], capture_output=True, text=True
            )

            if lint_result.returncode > 0 and lint_result.stdout.strip():
                issues.append(
                    {
                        "type": "lint_errors",
                        "description": f"Linting errors in {file_path}",
                        "details": lint_result.stdout,
                        "action": f"Fix the linting errors in {file_path}. These are likely syntax errors or undefined variables.",
                    }
                )

    # Check 4: Documentation
    for file_path in modified_files:
        if os.path.exists(file_path):
            with open(file_path, "r") as f:
                content = f.read()

            # Check if functions have docstrings
            functions_without_docs = []
            for match in re.finditer(
                r'^def\s+(\w+)\s*\([^)]*\):\s*\n(?!\s*("""|\'\'\'))',
                content,
                re.MULTILINE,
            ):
                functions_without_docs.append(match.group(1))

            if functions_without_docs:
                issues.append(
                    {
                        "type": "missing_docs",
                        "description": f"Functions without docstrings in {file_path}",
                        "details": f"Functions missing documentation: {', '.join(functions_without_docs)}",
                        "action": f"Add docstrings to these functions in {file_path}: {', '.join(functions_without_docs)}. Each docstring should describe what the function does, its parameters, and return value.",
                    }
                )

    return issues


# Main hook logic
def main():
    try:
        # Read input from Claude Code
        input_data = json.load(sys.stdin)

        # Extract session information
        session_id = input_data.get("session_id", "")
        transcript_path = input_data.get("transcript_path", "")
        stop_hook_active = input_data.get("stop_hook_active", False)

        # IMPORTANT: Prevent infinite loops
        # If we're already in a stop hook cycle, be very careful
        if stop_hook_active:
            print(
                "Stop hook is already active - allowing stop to prevent infinite loop",
                file=sys.stderr,
            )
            sys.exit(0)

        # Analyze what Claude did in this session
        modified_files, task_description = analyze_transcript(transcript_path)

        # If no Python files were modified, no need to run checks
        if not modified_files:
            print("No Python files modified - allowing stop", file=sys.stderr)
            sys.exit(0)

        print(
            f"Analyzing {len(modified_files)} modified Python files...", file=sys.stderr
        )

        # Run quality checks
        issues = run_quality_checks(modified_files)

        # Decide whether to block Claude from stopping
        if issues:
            # Build a clear message about what needs to be fixed
            message_parts = [
                "I've completed my quality checks and found some issues that need to be addressed:",
                "",
            ]

            for i, issue in enumerate(issues, 1):
                message_parts.append(f"{i}. {issue['description']}")
                message_parts.append(f"   Action needed: {issue['action']}")
                message_parts.append("")

            message_parts.append(
                "Please fix these issues before we can consider the task complete."
            )

            # Return JSON that blocks stopping and provides clear instructions
            output = {"decision": "block", "reason": "\n".join(message_parts)}

            print(json.dumps(output))
            sys.exit(0)

        # All checks passed!
        print("All quality checks passed - great work!", file=sys.stderr)
        sys.exit(0)

    except Exception as e:
        # Log the error but don't block Claude from stopping
        print(f"Error in quality hook: {str(e)}", file=sys.stderr)
        sys.exit(0)


if __name__ == "__main__":
    main()
