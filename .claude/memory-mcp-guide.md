# Memory MCP Usage Guide

## Overview
The Memory MCP server provides persistent memory across Claude Code sessions, allowing <PERSON> to remember project-specific information, decisions, patterns, and context between conversations.

## How Memory MCP Works

### Storage
- Memory is stored locally on your machine
- Persists between Claude Code sessions
- Each memory entry is timestamped
- Memories can be retrieved, updated, or deleted

### Memory Types
1. **Project Context** - Architecture, tech stack, conventions
2. **Debugging History** - Past bugs, solutions, patterns
3. **Personal Preferences** - Coding style, tools, workflows
4. **Decision Rationale** - Why certain choices were made
5. **Todo/Technical Debt** - What needs future attention

## Basic Commands

### Creating Memories
```
"Remember that [information to store]"
"Store in memory: [information]"
"Take note that [information]"
```

### Retrieving Memories
```
"What do you remember about [topic]?"
"Recall information about [topic]"
"Do you have any memories about [topic]?"
```

### Listing Memories
```
"Show all memories"
"List what you remember"
"What have you learned about this project?"
```

## Practical Examples

### 1. Project Setup Memory
```
"Remember that this project uses:
- Next.js 14 with App Router
- Supabase for backend
- Tailwind CSS with custom design system
- TypeScript with strict mode
- pnpm for package management"
```

### 2. Architecture Decisions
```
"Remember we chose PostgreSQL over MongoDB because:
- Need ACID compliance for financial data
- Complex relationships between entities
- Team expertise with SQL"
```

### 3. Debugging Context
```
"Remember the authentication bug pattern:
- Happens only with multiple tabs open
- Related to JWT token refresh race condition
- Temporary fix: increased token lifetime
- TODO: Implement proper token refresh queue"
```

### 4. Code Conventions
```
"Remember our coding conventions:
- Components: PascalCase with .tsx extension
- Utilities: camelCase in /lib folder
- API routes: RESTful naming in /api folder
- Git commits: conventional commits format
- PR titles: [JIRA-123] Brief description"
```

### 5. Personal Preferences
```
"Remember my preferences:
- Always use async/await over .then()
- Prefer Zod for validation
- Use early returns for error handling
- Write tests for critical paths only
- Comments only for complex logic"
```

## Advanced Usage

### Categorized Memories
```
"Remember for [AUTHENTICATION]: 
- Using Supabase Auth
- JWT stored in httpOnly cookies
- RLS policies for authorization
- No middleware auth checks"
```

### Contextual Updates
```
"Update memory: The payment bug is now fixed in v2.3.1"
"Append to memory about authentication: Now also supports OAuth"
```

### Memory Chains
Build complex understanding over time:
```
Session 1: "Remember our user model has email, name, role"
Session 2: "Remember users can have multiple organizations"
Session 3: "Remember organization roles override user roles"
→ Claude builds complete mental model
```

## Best Practices

### 1. Be Specific
❌ "Remember the bug"
✅ "Remember the Stripe webhook bug: duplicate events when server scales"

### 2. Include Context
❌ "Remember we use PostgreSQL"
✅ "Remember we use PostgreSQL 15 with pgvector extension for embeddings"

### 3. Update Regularly
- Mark fixed bugs as resolved
- Update deprecated patterns
- Refine understanding over time

### 4. Use Categories
- [SETUP] - Initial configuration
- [DEBUG] - Debugging history
- [ARCH] - Architecture decisions
- [TODO] - Future tasks
- [PATTERN] - Code patterns

## Example Workflow

### Day 1 - Project Start
```
"Remember project setup:
- Monorepo with Turborepo
- Apps: web (Next.js), mobile (React Native)
- Packages: ui, database, config
- Database: Supabase with custom RLS"
```

### Day 5 - First Bug
```
"Remember DEBUG: Image upload fails over 5MB
- Cause: Vercel function limit
- Solution: Direct upload to Supabase Storage
- TODO: Add progress indicator"
```

### Day 10 - Architecture Decision
```
"Remember ARCH decision: Chose tRPC over REST
- Type safety across monorepo
- Automatic client generation
- Better DX for team"
```

### Day 20 - Recall Everything
```
You: "I need to add file upload to mobile app"
Claude: "Based on memory: Use direct Supabase Storage upload 
(not through API due to 5MB Vercel limit). The web app 
implementation is in components/Upload.tsx. Need to add 
progress indicator as noted in TODO."
```

## Troubleshooting

### Memory Not Persisting?
1. Check MCP server is running: `claude mcp list`
2. Verify memory creation: "What did you just remember?"
3. Test retrieval: "What do you know about [topic]?"

### Too Many Memories?
- "List all memories about [topic]"
- "Delete memory about [old feature]"
- "Update memory: [corrected information]"

## Privacy & Security

- Memories are stored **locally** on your machine
- Not sent to Claude's servers
- Not shared between projects (unless in user scope)
- You control what gets remembered

## Quick Start Checklist

When starting a new project with Claude Code:

- [ ] Remember tech stack and versions
- [ ] Remember project structure
- [ ] Remember key conventions
- [ ] Remember API patterns
- [ ] Remember auth approach
- [ ] Remember deployment setup
- [ ] Remember team preferences

## Power User Tips

1. **Memory Templates**
   Create a template for new projects:
   ```
   "Remember standard setup: [paste your template]"
   ```

2. **Debugging Journal**
   ```
   "Remember debug log [date]: [what happened and solution]"
   ```

3. **Learning Patterns**
   ```
   "Remember every time X happens, check Y first"
   ```

4. **Context Switching**
   ```
   "What do you remember about [project name]?"
   ```

---

The Memory MCP transforms Claude Code from a stateless assistant into a knowledgeable team member who truly understands your project's history and context.