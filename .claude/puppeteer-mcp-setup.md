# Puppeteer MCP Server Setup Guide

## Overview
The Puppeteer MCP server has been successfully set up in your local environment. This server provides browser automation capabilities to Claude <PERSON> through the Model Context Protocol (MCP).

## Installation Complete ✓
- Server location: `/Applications/AI Project /myclaude/.claude/puppeteer-mcp/`
- Built files: `/Applications/AI Project /myclaude/.claude/puppeteer-mcp/dist/`
- Configuration: `/Applications/AI Project /myclaude/.claude/puppeteer-mcp-config.json`

## How to Add to Claude Code

### Method 1: Using Claude Code CLI (Recommended)
```bash
# Navigate to your project directory
cd "/Applications/AI Project /myclaude"

# Add the Puppeteer MCP server
claude mcp add puppeteer "node /Applications/AI Project /myclaude/.claude/puppeteer-mcp/dist/index.js"
```

### Method 2: Manual Configuration
Add the following to your Claude Code settings JSON file:
```json
{
  "mcpServers": {
    "puppeteer": {
      "command": "node",
      "args": ["/Applications/AI Project /myclaude/.claude/puppeteer-mcp/dist/index.js"],
      "transport": "stdio",
      "env": {
        "PUPPETEER_HEADLESS": "true"
      }
    }
  }
}
```

## Available Tools
Once configured, Claude Code will have access to these Puppeteer tools:

1. **puppeteer_navigate** - Navigate to URLs
2. **puppeteer_screenshot** - Capture screenshots
3. **puppeteer_click** - Click page elements
4. **puppeteer_hover** - Hover over elements
5. **puppeteer_fill** - Fill form inputs
6. **puppeteer_select** - Select dropdown options
7. **puppeteer_evaluate** - Execute JavaScript in browser

## Usage Examples

### Taking a Screenshot
```
Use the puppeteer_navigate tool to go to https://example.com
Then use puppeteer_screenshot to capture the page
```

### Form Automation
```
Navigate to a form page
Use puppeteer_fill to enter text in input fields
Use puppeteer_click to submit the form
```

### Web Scraping
```
Navigate to the target page
Use puppeteer_evaluate to extract data using JavaScript
```

## Configuration Options

### Headless Mode
By default, the browser runs in headless mode. To see the browser window:
1. Remove `"PUPPETEER_HEADLESS": "true"` from the env configuration
2. Or set it to `"false"`

### Custom Launch Options
You can modify the server's behavior by editing environment variables in the configuration.

## Verification Steps
After adding the server to Claude Code:

1. Run `claude mcp list` to verify the server appears
2. Test with: "Navigate to https://example.com using Puppeteer"
3. Check server logs if there are issues

## Security Considerations
- The Puppeteer server can access local files and internal networks
- Only use with trusted websites
- Be cautious with JavaScript execution
- Consider running in a sandboxed environment for sensitive operations

## Troubleshooting

### Server Not Starting
- Check Node.js is installed: `node --version`
- Verify file paths are correct
- Check permissions on the dist/index.js file

### Browser Issues
- Puppeteer will download Chromium on first run
- Ensure sufficient disk space
- On some systems, additional dependencies may be needed

### Connection Issues
- Verify the server starts successfully
- Check Claude Code logs for error messages
- Ensure no firewall blocking local connections

## Next Steps
1. Add the server to Claude Code using the CLI command above
2. Test basic navigation and screenshot functionality
3. Explore advanced features like form automation
4. Consider adding custom configurations for your use cases