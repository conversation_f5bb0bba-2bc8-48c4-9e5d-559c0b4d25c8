var format = require('util').format;

var common = require('./common');

common.register('echo', _echo, {
  allowGlobbing: false,
});

//@
//@ ### echo([options,] string [, string ...])
//@
//@ Available options:
//@
//@ + `-e`: interpret backslash escapes (default)
//@ + `-n`: remove trailing newline from output
//@
//@ Examples:
//@
//@ ```javascript
//@ echo('hello world');
//@ var str = echo('hello world');
//@ echo('-n', 'no newline at end');
//@ ```
//@
//@ Prints `string` to stdout, and returns string with additional utility methods
//@ like `.to()`.
function _echo(opts) {
  // allow strings starting with '-', see issue #20
  var messages = [].slice.call(arguments, opts ? 0 : 1);
  var options = {};

  // If the first argument starts with '-', parse it as options string.
  // If parseOptions throws, it wasn't an options string.
  try {
    options = common.parseOptions(messages[0], {
      'e': 'escapes',
      'n': 'no_newline',
    }, {
      silent: true,
    });

    // Allow null to be echoed
    if (messages[0]) {
      messages.shift();
    }
  } catch (_) {
    // Clear out error if an error occurred
    common.state.error = null;
  }

  var output = format.apply(null, messages);

  // Add newline if -n is not passed.
  if (!options.no_newline) {
    output += '\n';
  }

  process.stdout.write(output);

  return output;
}

module.exports = _echo;
