{"name": "data-uri-to-buffer", "version": "6.0.2", "description": "Create an ArrayBuffer instance from a Data URI string", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"node": "./dist/node.js", "default": "./dist/index.js"}, "files": ["dist"], "repository": {"type": "git", "url": "https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/data-uri-to-buffer"}, "engines": {"node": ">= 14"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^14.18.45", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "tsconfig": "0.0.0"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}}