{"version": 3, "file": "Browser.js", "sourceRoot": "", "sources": ["../../../../src/api/Browser.ts"], "names": [], "mappings": ";;;AAUA,4DAKwC;AAIxC,+DAAuE;AACvE,+CAM2B;AAC3B,yDAAwE;AA0CxE;;GAEG;AACU,QAAA,qCAAqC,GAAG,IAAI,GAAG,CAG1D;IACA,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,eAAe,EAAE,eAAe,CAAC;IAClC,iCAAiC;IACjC,oBAAoB;IACpB,CAAC,QAAQ,EAAE,cAAc,CAAC;IAC1B,CAAC,YAAY,EAAE,cAAc,CAAC;IAC9B,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;IACrC,CAAC,sBAAsB,EAAE,SAAS,CAAC;IACnC,CAAC,eAAe,EAAE,SAAS,CAAC;IAC5B,CAAC,WAAW,EAAE,SAAS,CAAC;IACxB,CAAC,cAAc,EAAE,SAAS,CAAC;IAC3B,CAAC,sBAAsB,EAAE,qBAAqB,CAAC;IAC/C,CAAC,gBAAgB,EAAE,oBAAoB,CAAC;IACxC,CAAC,iBAAiB,EAAE,oBAAoB,CAAC;IACzC,CAAC,2BAA2B,EAAE,yBAAyB,CAAC;IACxD,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;IACrC,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IACxC,CAAC,gBAAgB,EAAE,eAAe,CAAC;IACnC,uCAAuC;IACvC,CAAC,YAAY,EAAE,WAAW,CAAC;CAC5B,CAAC,CAAC;AA+GH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsCG;AACH,MAAsB,OAAQ,SAAQ,8BAA2B;IAC/D;;OAEG;IACH;QACE,KAAK,EAAE,CAAC;IACV,CAAC;IAsFD;;;;;;;;;;;;;;OAcG;IACH,KAAK,CAAC,aAAa,CACjB,SAAoD,EACpD,UAAgC,EAAE;QAElC,MAAM,EAAC,OAAO,EAAE,EAAE,GAAG,KAAK,EAAE,MAAM,EAAC,GAAG,OAAO,CAAC;QAC9C,OAAO,MAAM,IAAA,wBAAc,EACzB,IAAA,eAAK,EACH,IAAA,0BAAgB,EAAC,IAAI,mDAA6B,EAClD,IAAA,0BAAgB,EAAC,IAAI,mDAA6B,EAClD,IAAA,cAAI,EAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CACrB,CAAC,IAAI,CACJ,IAAA,qBAAW,EAAC,SAAS,CAAC,EACtB,IAAA,kBAAQ,EAAC,IAAA,yBAAe,EAAC,MAAM,CAAC,EAAE,IAAA,iBAAO,EAAC,EAAE,CAAC,CAAC,CAC/C,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CACpC,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACnC,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC,CAAC,CACH,CAAC;QACF,iBAAiB;QACjB,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;YACpC,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAoCD;;;;;;;OAOG;IACH,KAAK,CAAC,OAAO;QACX,OAAO,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC,OAAO,EAAE,CAAC;IACtD,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,SAAS,CAAC,GAAG,OAAiB;QAClC,OAAO,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,YAAY,CAAC,GAAG,OAAiB;QACrC,OAAO,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC,YAAY,CAAC,GAAG,OAAO,CAAC,CAAC;IACrE,CAAC;IAED;;;;OAIG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAOD,gBAAgB;IACP,CAAC,6BAAa,CAAC;QACtB,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YACnB,OAAO,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,oBAAU,CAAC,CAAC;QAC7C,CAAC;QACD,OAAO,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,oBAAU,CAAC,CAAC;IAClD,CAAC;IAED,gBAAgB;IAChB,CAAC,kCAAkB,CAAC;QAClB,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;CAkBF;AAtQD,0BAsQC"}