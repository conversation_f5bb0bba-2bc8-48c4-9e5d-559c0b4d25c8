{"version": 3, "file": "Navigation.js", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/Navigation.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,kEAA0D;AAC1D,4DAAyD;AACzD,4DAAwE;AAaxE;;GAEG;IACU,UAAU;;sBAAS,8BAAY;;;iBAA/B,UAAW,SAAQ,WAS9B;;;YAmIA,wKAAQ,OAAO,6DAEd;;;QApID,MAAM,CAAC,IAAI,CAAC,OAAwB;YAClC,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;YAC3C,UAAU,CAAC,WAAW,EAAE,CAAC;YACzB,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,QAAQ,GAhBG,mDAAU,CAgBS;QAC9B,WAAW,CAAyB;QAC3B,gBAAgB,CAAkB;QAClC,YAAY,GAAG,IAAI,+BAAe,EAAE,CAAC;QAC9C,GAAG,CAAiB;QAEpB,YAAoB,OAAwB;YAC1C,KAAK,EAAE,CAAC;YAER,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;QAClC,CAAC;QAED,WAAW;YACT,MAAM,sBAAsB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAClD,IAAI,8BAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CACxC,CAAC;YACF,sBAAsB,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACzC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAClB,GAAG,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG;oBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBACH,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;YAEH,sBAAsB,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAC,EAAE,EAAE;gBACjD,IACE,OAAO,CAAC,UAAU,KAAK,SAAS;oBAChC,wEAAwE;oBACxE,uBAAuB;oBACvB,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,EAClC,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;gBACxB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBAC9B,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAC1C,IAAI,8BAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAChC,CAAC;gBAEF,cAAc,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE;oBACtC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;gBAC1B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAC1C,IAAI,8BAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAChC,CAAC;YACF,cAAc,CAAC,EAAE,CAAC,mCAAmC,EAAE,IAAI,CAAC,EAAE;gBAC5D,IACE,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE;oBACzC,IAAI,CAAC,WAAW,KAAK,SAAS,EAC9B,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;YAEH,KAAK,MAAM,SAAS,IAAI;gBACtB,kCAAkC;gBAClC,sBAAsB;aACd,EAAE,CAAC;gBACX,cAAc,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE;oBAClC,IACE,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE;wBACzC,IAAI,CAAC,UAAU,KAAK,IAAI;wBACxB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAC/B,CAAC;wBACD,OAAO;oBACT,CAAC;oBAED,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,CAAC,CAAC,CAAC;YACL,CAAC;YAED,KAAK,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI;gBAC/B,CAAC,mCAAmC,EAAE,UAAU,CAAC;gBACjD,CAAC,kCAAkC,EAAE,QAAQ,CAAC;gBAC9C,CAAC,mCAAmC,EAAE,SAAS,CAAC;aACxC,EAAE,CAAC;gBACX,cAAc,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE;oBAClC,IACE,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE;wBACzC,qEAAqE;wBACrE,sBAAsB;wBACtB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAC/B,CAAC;wBACD,OAAO;oBACT,CAAC;oBAED,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;wBACf,GAAG,EAAE,IAAI,CAAC,GAAG;wBACb,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;qBACpC,CAAC,CAAC;oBACH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,QAAQ,CAAC,UAAyB;YAChC,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;gBACjE,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;gBAC3B,IAAI,CAAC,GAAG,GAAG,UAAU,CAAC;gBACtB,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,IAAI,CAAC,GAAG,KAAK,UAAU,CAAC;QACjC,CAAC;QAED,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC;QAC3D,CAAC;QACD,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;QACpC,CAAC;QACD,IAAI,OAAO;YACT,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC;QACD,IAAI,UAAU;YACZ,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,CAAC;QAGO,OAAO;YACb,IAAI,CAAC,6BAAa,CAAC,EAAE,CAAC;QACxB,CAAC;QAEQ,yBALR,+BAAe,GAKN,6BAAa,EAAC;YACtB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,KAAK,CAAC,6BAAa,CAAC,EAAE,CAAC;QACzB,CAAC;;;AAnJU,gCAAU"}