{"version": 3, "file": "chromium.js", "sourceRoot": "", "sources": ["../../../src/browser-data/chromium.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,IAAI,MAAM,MAAM,CAAC;AAExB,OAAO,EAAC,OAAO,EAAC,MAAM,gBAAgB,CAAC;AAEvC,OAAO,EAAC,eAAe,EAAC,MAAM,YAAY,CAAC;AAE3C,SAAS,OAAO,CAAC,QAAyB,EAAE,OAAe;IACzD,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,eAAe,CAAC,KAAK;YACxB,OAAO,cAAc,CAAC;QACxB,KAAK,eAAe,CAAC,OAAO,CAAC;QAC7B,KAAK,eAAe,CAAC,GAAG;YACtB,OAAO,YAAY,CAAC;QACtB,KAAK,eAAe,CAAC,KAAK,CAAC;QAC3B,KAAK,eAAe,CAAC,KAAK;YACxB,2CAA2C;YAC3C,OAAO,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC;IAC1E,CAAC;AACH,CAAC;AAED,SAAS,MAAM,CAAC,QAAyB;IACvC,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,eAAe,CAAC,KAAK;YACxB,OAAO,WAAW,CAAC;QACrB,KAAK,eAAe,CAAC,OAAO;YAC1B,OAAO,SAAS,CAAC;QACnB,KAAK,eAAe,CAAC,GAAG;YACtB,OAAO,KAAK,CAAC;QACf,KAAK,eAAe,CAAC,KAAK;YACxB,OAAO,KAAK,CAAC;QACf,KAAK,eAAe,CAAC,KAAK;YACxB,OAAO,SAAS,CAAC;IACrB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,kBAAkB,CAChC,QAAyB,EACzB,OAAe,EACf,OAAO,GAAG,2DAA2D;IAErE,OAAO,GAAG,OAAO,IAAI,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;AAC1E,CAAC;AAED,MAAM,UAAU,mBAAmB,CACjC,QAAyB,EACzB,OAAe;IAEf,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AAC1E,CAAC;AAED,MAAM,UAAU,sBAAsB,CACpC,QAAyB,EACzB,QAAgB;IAEhB,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,eAAe,CAAC,GAAG,CAAC;QACzB,KAAK,eAAe,CAAC,OAAO;YAC1B,OAAO,IAAI,CAAC,IAAI,CACd,YAAY,EACZ,cAAc,EACd,UAAU,EACV,OAAO,EACP,UAAU,CACX,CAAC;QACJ,KAAK,eAAe,CAAC,KAAK;YACxB,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAC7C,KAAK,eAAe,CAAC,KAAK,CAAC;QAC3B,KAAK,eAAe,CAAC,KAAK;YACxB,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;IACjD,CAAC;AACH,CAAC;AACD,MAAM,CAAC,KAAK,UAAU,cAAc,CAClC,QAAyB;IAEzB,OAAO,MAAM,OAAO,CAClB,IAAI,GAAG,CACL,6DAA6D,MAAM,CACjE,QAAQ,CACT,cAAc,CAChB,CACF,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,CAAS,EAAE,CAAS;IAClD,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC"}