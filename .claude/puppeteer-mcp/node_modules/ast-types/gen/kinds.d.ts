import { namedTypes } from "./namedTypes";
export declare type PrintableKind = namedTypes.File | namedTypes.Program | namedTypes.Identifier | namedTypes.BlockStatement | namedTypes.EmptyStatement | namedTypes.ExpressionStatement | namedTypes.IfStatement | namedTypes.LabeledStatement | namedTypes.BreakStatement | namedTypes.ContinueStatement | namedTypes.WithStatement | namedTypes.SwitchStatement | namedTypes.SwitchCase | namedTypes.ReturnStatement | namedTypes.ThrowStatement | namedTypes.TryStatement | namedTypes.CatchClause | namedTypes.WhileStatement | namedTypes.DoWhileStatement | namedTypes.ForStatement | namedTypes.VariableDeclaration | namedTypes.ForInStatement | namedTypes.DebuggerStatement | namedTypes.FunctionDeclaration | namedTypes.FunctionExpression | namedTypes.VariableDeclarator | namedTypes.ThisExpression | namedTypes.ArrayExpression | namedTypes.ObjectExpression | namedTypes.Property | namedTypes.Literal | namedTypes.SequenceExpression | namedTypes.UnaryExpression | namedTypes.BinaryExpression | namedTypes.AssignmentExpression | namedTypes.MemberExpression | namedTypes.UpdateExpression | namedTypes.LogicalExpression | namedTypes.ConditionalExpression | namedTypes.NewExpression | namedTypes.CallExpression | namedTypes.RestElement | namedTypes.TypeAnnotation | namedTypes.TSTypeAnnotation | namedTypes.SpreadElementPattern | namedTypes.ArrowFunctionExpression | namedTypes.ForOfStatement | namedTypes.YieldExpression | namedTypes.GeneratorExpression | namedTypes.ComprehensionBlock | namedTypes.ComprehensionExpression | namedTypes.ObjectProperty | namedTypes.PropertyPattern | namedTypes.ObjectPattern | namedTypes.ArrayPattern | namedTypes.MethodDefinition | namedTypes.SpreadElement | namedTypes.AssignmentPattern | namedTypes.ClassPropertyDefinition | namedTypes.ClassProperty | namedTypes.ClassBody | namedTypes.ClassDeclaration | namedTypes.ClassExpression | namedTypes.ImportSpecifier | namedTypes.ImportNamespaceSpecifier | namedTypes.ImportDefaultSpecifier | namedTypes.ImportDeclaration | namedTypes.TaggedTemplateExpression | namedTypes.TemplateLiteral | namedTypes.TemplateElement | namedTypes.SpreadProperty | namedTypes.SpreadPropertyPattern | namedTypes.AwaitExpression | namedTypes.ImportExpression | namedTypes.JSXAttribute | namedTypes.JSXIdentifier | namedTypes.JSXNamespacedName | namedTypes.JSXExpressionContainer | namedTypes.JSXMemberExpression | namedTypes.JSXSpreadAttribute | namedTypes.JSXElement | namedTypes.JSXOpeningElement | namedTypes.JSXClosingElement | namedTypes.JSXFragment | namedTypes.JSXText | namedTypes.JSXOpeningFragment | namedTypes.JSXClosingFragment | namedTypes.JSXEmptyExpression | namedTypes.JSXSpreadChild | namedTypes.TypeParameterDeclaration | namedTypes.TSTypeParameterDeclaration | namedTypes.TypeParameterInstantiation | namedTypes.TSTypeParameterInstantiation | namedTypes.ClassImplements | namedTypes.TSExpressionWithTypeArguments | namedTypes.AnyTypeAnnotation | namedTypes.EmptyTypeAnnotation | namedTypes.MixedTypeAnnotation | namedTypes.VoidTypeAnnotation | namedTypes.NumberTypeAnnotation | namedTypes.NumberLiteralTypeAnnotation | namedTypes.NumericLiteralTypeAnnotation | namedTypes.StringTypeAnnotation | namedTypes.StringLiteralTypeAnnotation | namedTypes.BooleanTypeAnnotation | namedTypes.BooleanLiteralTypeAnnotation | namedTypes.NullableTypeAnnotation | namedTypes.NullLiteralTypeAnnotation | namedTypes.NullTypeAnnotation | namedTypes.ThisTypeAnnotation | namedTypes.ExistsTypeAnnotation | namedTypes.ExistentialTypeParam | namedTypes.FunctionTypeAnnotation | namedTypes.FunctionTypeParam | namedTypes.ArrayTypeAnnotation | namedTypes.ObjectTypeAnnotation | namedTypes.ObjectTypeProperty | namedTypes.ObjectTypeSpreadProperty | namedTypes.ObjectTypeIndexer | namedTypes.ObjectTypeCallProperty | namedTypes.ObjectTypeInternalSlot | namedTypes.Variance | namedTypes.QualifiedTypeIdentifier | namedTypes.GenericTypeAnnotation | namedTypes.MemberTypeAnnotation | namedTypes.UnionTypeAnnotation | namedTypes.IntersectionTypeAnnotation | namedTypes.TypeofTypeAnnotation | namedTypes.TypeParameter | namedTypes.InterfaceTypeAnnotation | namedTypes.InterfaceExtends | namedTypes.InterfaceDeclaration | namedTypes.DeclareInterface | namedTypes.TypeAlias | namedTypes.OpaqueType | namedTypes.DeclareTypeAlias | namedTypes.DeclareOpaqueType | namedTypes.TypeCastExpression | namedTypes.TupleTypeAnnotation | namedTypes.DeclareVariable | namedTypes.DeclareFunction | namedTypes.DeclareClass | namedTypes.DeclareModule | namedTypes.DeclareModuleExports | namedTypes.DeclareExportDeclaration | namedTypes.ExportSpecifier | namedTypes.ExportBatchSpecifier | namedTypes.DeclareExportAllDeclaration | namedTypes.InferredPredicate | namedTypes.DeclaredPredicate | namedTypes.ExportDeclaration | namedTypes.Block | namedTypes.Line | namedTypes.Noop | namedTypes.DoExpression | namedTypes.Super | namedTypes.BindExpression | namedTypes.Decorator | namedTypes.MetaProperty | namedTypes.ParenthesizedExpression | namedTypes.ExportDefaultDeclaration | namedTypes.ExportNamedDeclaration | namedTypes.ExportNamespaceSpecifier | namedTypes.ExportDefaultSpecifier | namedTypes.ExportAllDeclaration | namedTypes.CommentBlock | namedTypes.CommentLine | namedTypes.Directive | namedTypes.DirectiveLiteral | namedTypes.InterpreterDirective | namedTypes.StringLiteral | namedTypes.NumericLiteral | namedTypes.BigIntLiteral | namedTypes.NullLiteral | namedTypes.BooleanLiteral | namedTypes.RegExpLiteral | namedTypes.ObjectMethod | namedTypes.ClassPrivateProperty | namedTypes.ClassMethod | namedTypes.ClassPrivateMethod | namedTypes.PrivateName | namedTypes.RestProperty | namedTypes.ForAwaitStatement | namedTypes.Import | namedTypes.TSQualifiedName | namedTypes.TSTypeReference | namedTypes.TSAsExpression | namedTypes.TSNonNullExpression | namedTypes.TSAnyKeyword | namedTypes.TSBigIntKeyword | namedTypes.TSBooleanKeyword | namedTypes.TSNeverKeyword | namedTypes.TSNullKeyword | namedTypes.TSNumberKeyword | namedTypes.TSObjectKeyword | namedTypes.TSStringKeyword | namedTypes.TSSymbolKeyword | namedTypes.TSUndefinedKeyword | namedTypes.TSUnknownKeyword | namedTypes.TSVoidKeyword | namedTypes.TSThisType | namedTypes.TSArrayType | namedTypes.TSLiteralType | namedTypes.TSUnionType | namedTypes.TSIntersectionType | namedTypes.TSConditionalType | namedTypes.TSInferType | namedTypes.TSTypeParameter | namedTypes.TSParenthesizedType | namedTypes.TSFunctionType | namedTypes.TSConstructorType | namedTypes.TSDeclareFunction | namedTypes.TSDeclareMethod | namedTypes.TSMappedType | namedTypes.TSTupleType | namedTypes.TSNamedTupleMember | namedTypes.TSRestType | namedTypes.TSOptionalType | namedTypes.TSIndexedAccessType | namedTypes.TSTypeOperator | namedTypes.TSIndexSignature | namedTypes.TSPropertySignature | namedTypes.TSMethodSignature | namedTypes.TSTypePredicate | namedTypes.TSCallSignatureDeclaration | namedTypes.TSConstructSignatureDeclaration | namedTypes.TSEnumMember | namedTypes.TSTypeQuery | namedTypes.TSImportType | namedTypes.TSTypeLiteral | namedTypes.TSTypeAssertion | namedTypes.TSEnumDeclaration | namedTypes.TSTypeAliasDeclaration | namedTypes.TSModuleBlock | namedTypes.TSModuleDeclaration | namedTypes.TSImportEqualsDeclaration | namedTypes.TSExternalModuleReference | namedTypes.TSExportAssignment | namedTypes.TSNamespaceExportDeclaration | namedTypes.TSInterfaceBody | namedTypes.TSInterfaceDeclaration | namedTypes.TSParameterProperty | namedTypes.OptionalMemberExpression | namedTypes.OptionalCallExpression;
export declare type SourceLocationKind = namedTypes.SourceLocation;
export declare type NodeKind = namedTypes.File | namedTypes.Program | namedTypes.Identifier | namedTypes.BlockStatement | namedTypes.EmptyStatement | namedTypes.ExpressionStatement | namedTypes.IfStatement | namedTypes.LabeledStatement | namedTypes.BreakStatement | namedTypes.ContinueStatement | namedTypes.WithStatement | namedTypes.SwitchStatement | namedTypes.SwitchCase | namedTypes.ReturnStatement | namedTypes.ThrowStatement | namedTypes.TryStatement | namedTypes.CatchClause | namedTypes.WhileStatement | namedTypes.DoWhileStatement | namedTypes.ForStatement | namedTypes.VariableDeclaration | namedTypes.ForInStatement | namedTypes.DebuggerStatement | namedTypes.FunctionDeclaration | namedTypes.FunctionExpression | namedTypes.VariableDeclarator | namedTypes.ThisExpression | namedTypes.ArrayExpression | namedTypes.ObjectExpression | namedTypes.Property | namedTypes.Literal | namedTypes.SequenceExpression | namedTypes.UnaryExpression | namedTypes.BinaryExpression | namedTypes.AssignmentExpression | namedTypes.MemberExpression | namedTypes.UpdateExpression | namedTypes.LogicalExpression | namedTypes.ConditionalExpression | namedTypes.NewExpression | namedTypes.CallExpression | namedTypes.RestElement | namedTypes.TypeAnnotation | namedTypes.TSTypeAnnotation | namedTypes.SpreadElementPattern | namedTypes.ArrowFunctionExpression | namedTypes.ForOfStatement | namedTypes.YieldExpression | namedTypes.GeneratorExpression | namedTypes.ComprehensionBlock | namedTypes.ComprehensionExpression | namedTypes.ObjectProperty | namedTypes.PropertyPattern | namedTypes.ObjectPattern | namedTypes.ArrayPattern | namedTypes.MethodDefinition | namedTypes.SpreadElement | namedTypes.AssignmentPattern | namedTypes.ClassPropertyDefinition | namedTypes.ClassProperty | namedTypes.ClassBody | namedTypes.ClassDeclaration | namedTypes.ClassExpression | namedTypes.ImportSpecifier | namedTypes.ImportNamespaceSpecifier | namedTypes.ImportDefaultSpecifier | namedTypes.ImportDeclaration | namedTypes.TaggedTemplateExpression | namedTypes.TemplateLiteral | namedTypes.TemplateElement | namedTypes.SpreadProperty | namedTypes.SpreadPropertyPattern | namedTypes.AwaitExpression | namedTypes.ImportExpression | namedTypes.JSXAttribute | namedTypes.JSXIdentifier | namedTypes.JSXNamespacedName | namedTypes.JSXExpressionContainer | namedTypes.JSXMemberExpression | namedTypes.JSXSpreadAttribute | namedTypes.JSXElement | namedTypes.JSXOpeningElement | namedTypes.JSXClosingElement | namedTypes.JSXFragment | namedTypes.JSXText | namedTypes.JSXOpeningFragment | namedTypes.JSXClosingFragment | namedTypes.JSXEmptyExpression | namedTypes.JSXSpreadChild | namedTypes.TypeParameterDeclaration | namedTypes.TSTypeParameterDeclaration | namedTypes.TypeParameterInstantiation | namedTypes.TSTypeParameterInstantiation | namedTypes.ClassImplements | namedTypes.TSExpressionWithTypeArguments | namedTypes.AnyTypeAnnotation | namedTypes.EmptyTypeAnnotation | namedTypes.MixedTypeAnnotation | namedTypes.VoidTypeAnnotation | namedTypes.NumberTypeAnnotation | namedTypes.NumberLiteralTypeAnnotation | namedTypes.NumericLiteralTypeAnnotation | namedTypes.StringTypeAnnotation | namedTypes.StringLiteralTypeAnnotation | namedTypes.BooleanTypeAnnotation | namedTypes.BooleanLiteralTypeAnnotation | namedTypes.NullableTypeAnnotation | namedTypes.NullLiteralTypeAnnotation | namedTypes.NullTypeAnnotation | namedTypes.ThisTypeAnnotation | namedTypes.ExistsTypeAnnotation | namedTypes.ExistentialTypeParam | namedTypes.FunctionTypeAnnotation | namedTypes.FunctionTypeParam | namedTypes.ArrayTypeAnnotation | namedTypes.ObjectTypeAnnotation | namedTypes.ObjectTypeProperty | namedTypes.ObjectTypeSpreadProperty | namedTypes.ObjectTypeIndexer | namedTypes.ObjectTypeCallProperty | namedTypes.ObjectTypeInternalSlot | namedTypes.Variance | namedTypes.QualifiedTypeIdentifier | namedTypes.GenericTypeAnnotation | namedTypes.MemberTypeAnnotation | namedTypes.UnionTypeAnnotation | namedTypes.IntersectionTypeAnnotation | namedTypes.TypeofTypeAnnotation | namedTypes.TypeParameter | namedTypes.InterfaceTypeAnnotation | namedTypes.InterfaceExtends | namedTypes.InterfaceDeclaration | namedTypes.DeclareInterface | namedTypes.TypeAlias | namedTypes.OpaqueType | namedTypes.DeclareTypeAlias | namedTypes.DeclareOpaqueType | namedTypes.TypeCastExpression | namedTypes.TupleTypeAnnotation | namedTypes.DeclareVariable | namedTypes.DeclareFunction | namedTypes.DeclareClass | namedTypes.DeclareModule | namedTypes.DeclareModuleExports | namedTypes.DeclareExportDeclaration | namedTypes.ExportSpecifier | namedTypes.ExportBatchSpecifier | namedTypes.DeclareExportAllDeclaration | namedTypes.InferredPredicate | namedTypes.DeclaredPredicate | namedTypes.ExportDeclaration | namedTypes.Noop | namedTypes.DoExpression | namedTypes.Super | namedTypes.BindExpression | namedTypes.Decorator | namedTypes.MetaProperty | namedTypes.ParenthesizedExpression | namedTypes.ExportDefaultDeclaration | namedTypes.ExportNamedDeclaration | namedTypes.ExportNamespaceSpecifier | namedTypes.ExportDefaultSpecifier | namedTypes.ExportAllDeclaration | namedTypes.Directive | namedTypes.DirectiveLiteral | namedTypes.InterpreterDirective | namedTypes.StringLiteral | namedTypes.NumericLiteral | namedTypes.BigIntLiteral | namedTypes.NullLiteral | namedTypes.BooleanLiteral | namedTypes.RegExpLiteral | namedTypes.ObjectMethod | namedTypes.ClassPrivateProperty | namedTypes.ClassMethod | namedTypes.ClassPrivateMethod | namedTypes.PrivateName | namedTypes.RestProperty | namedTypes.ForAwaitStatement | namedTypes.Import | namedTypes.TSQualifiedName | namedTypes.TSTypeReference | namedTypes.TSAsExpression | namedTypes.TSNonNullExpression | namedTypes.TSAnyKeyword | namedTypes.TSBigIntKeyword | namedTypes.TSBooleanKeyword | namedTypes.TSNeverKeyword | namedTypes.TSNullKeyword | namedTypes.TSNumberKeyword | namedTypes.TSObjectKeyword | namedTypes.TSStringKeyword | namedTypes.TSSymbolKeyword | namedTypes.TSUndefinedKeyword | namedTypes.TSUnknownKeyword | namedTypes.TSVoidKeyword | namedTypes.TSThisType | namedTypes.TSArrayType | namedTypes.TSLiteralType | namedTypes.TSUnionType | namedTypes.TSIntersectionType | namedTypes.TSConditionalType | namedTypes.TSInferType | namedTypes.TSTypeParameter | namedTypes.TSParenthesizedType | namedTypes.TSFunctionType | namedTypes.TSConstructorType | namedTypes.TSDeclareFunction | namedTypes.TSDeclareMethod | namedTypes.TSMappedType | namedTypes.TSTupleType | namedTypes.TSNamedTupleMember | namedTypes.TSRestType | namedTypes.TSOptionalType | namedTypes.TSIndexedAccessType | namedTypes.TSTypeOperator | namedTypes.TSIndexSignature | namedTypes.TSPropertySignature | namedTypes.TSMethodSignature | namedTypes.TSTypePredicate | namedTypes.TSCallSignatureDeclaration | namedTypes.TSConstructSignatureDeclaration | namedTypes.TSEnumMember | namedTypes.TSTypeQuery | namedTypes.TSImportType | namedTypes.TSTypeLiteral | namedTypes.TSTypeAssertion | namedTypes.TSEnumDeclaration | namedTypes.TSTypeAliasDeclaration | namedTypes.TSModuleBlock | namedTypes.TSModuleDeclaration | namedTypes.TSImportEqualsDeclaration | namedTypes.TSExternalModuleReference | namedTypes.TSExportAssignment | namedTypes.TSNamespaceExportDeclaration | namedTypes.TSInterfaceBody | namedTypes.TSInterfaceDeclaration | namedTypes.TSParameterProperty | namedTypes.OptionalMemberExpression | namedTypes.OptionalCallExpression;
export declare type CommentKind = namedTypes.Block | namedTypes.Line | namedTypes.CommentBlock | namedTypes.CommentLine;
export declare type PositionKind = namedTypes.Position;
export declare type FileKind = namedTypes.File;
export declare type ProgramKind = namedTypes.Program;
export declare type StatementKind = namedTypes.BlockStatement | namedTypes.EmptyStatement | namedTypes.ExpressionStatement | namedTypes.IfStatement | namedTypes.LabeledStatement | namedTypes.BreakStatement | namedTypes.ContinueStatement | namedTypes.WithStatement | namedTypes.SwitchStatement | namedTypes.ReturnStatement | namedTypes.ThrowStatement | namedTypes.TryStatement | namedTypes.WhileStatement | namedTypes.DoWhileStatement | namedTypes.ForStatement | namedTypes.VariableDeclaration | namedTypes.ForInStatement | namedTypes.DebuggerStatement | namedTypes.FunctionDeclaration | namedTypes.ForOfStatement | namedTypes.MethodDefinition | namedTypes.ClassPropertyDefinition | namedTypes.ClassProperty | namedTypes.ClassBody | namedTypes.ClassDeclaration | namedTypes.ImportDeclaration | namedTypes.TSTypeParameterDeclaration | namedTypes.InterfaceDeclaration | namedTypes.DeclareInterface | namedTypes.TypeAlias | namedTypes.OpaqueType | namedTypes.DeclareTypeAlias | namedTypes.DeclareOpaqueType | namedTypes.DeclareVariable | namedTypes.DeclareFunction | namedTypes.DeclareClass | namedTypes.DeclareModule | namedTypes.DeclareModuleExports | namedTypes.DeclareExportDeclaration | namedTypes.DeclareExportAllDeclaration | namedTypes.ExportDeclaration | namedTypes.Noop | namedTypes.ExportDefaultDeclaration | namedTypes.ExportNamedDeclaration | namedTypes.ExportAllDeclaration | namedTypes.ClassPrivateProperty | namedTypes.ClassMethod | namedTypes.ClassPrivateMethod | namedTypes.ForAwaitStatement | namedTypes.TSDeclareFunction | namedTypes.TSDeclareMethod | namedTypes.TSIndexSignature | namedTypes.TSPropertySignature | namedTypes.TSMethodSignature | namedTypes.TSCallSignatureDeclaration | namedTypes.TSConstructSignatureDeclaration | namedTypes.TSEnumDeclaration | namedTypes.TSTypeAliasDeclaration | namedTypes.TSModuleDeclaration | namedTypes.TSImportEqualsDeclaration | namedTypes.TSExternalModuleReference | namedTypes.TSExportAssignment | namedTypes.TSNamespaceExportDeclaration | namedTypes.TSInterfaceDeclaration;
export declare type FunctionKind = namedTypes.FunctionDeclaration | namedTypes.FunctionExpression | namedTypes.ArrowFunctionExpression | namedTypes.ObjectMethod | namedTypes.ClassMethod | namedTypes.ClassPrivateMethod;
export declare type ExpressionKind = namedTypes.Identifier | namedTypes.FunctionExpression | namedTypes.ThisExpression | namedTypes.ArrayExpression | namedTypes.ObjectExpression | namedTypes.Literal | namedTypes.SequenceExpression | namedTypes.UnaryExpression | namedTypes.BinaryExpression | namedTypes.AssignmentExpression | namedTypes.MemberExpression | namedTypes.UpdateExpression | namedTypes.LogicalExpression | namedTypes.ConditionalExpression | namedTypes.NewExpression | namedTypes.CallExpression | namedTypes.ArrowFunctionExpression | namedTypes.YieldExpression | namedTypes.GeneratorExpression | namedTypes.ComprehensionExpression | namedTypes.ClassExpression | namedTypes.TaggedTemplateExpression | namedTypes.TemplateLiteral | namedTypes.AwaitExpression | namedTypes.ImportExpression | namedTypes.JSXIdentifier | namedTypes.JSXExpressionContainer | namedTypes.JSXMemberExpression | namedTypes.JSXElement | namedTypes.JSXFragment | namedTypes.JSXText | namedTypes.JSXEmptyExpression | namedTypes.JSXSpreadChild | namedTypes.TypeCastExpression | namedTypes.DoExpression | namedTypes.Super | namedTypes.BindExpression | namedTypes.MetaProperty | namedTypes.ParenthesizedExpression | namedTypes.DirectiveLiteral | namedTypes.StringLiteral | namedTypes.NumericLiteral | namedTypes.BigIntLiteral | namedTypes.NullLiteral | namedTypes.BooleanLiteral | namedTypes.RegExpLiteral | namedTypes.PrivateName | namedTypes.Import | namedTypes.TSAsExpression | namedTypes.TSNonNullExpression | namedTypes.TSTypeParameter | namedTypes.TSTypeAssertion | namedTypes.OptionalMemberExpression | namedTypes.OptionalCallExpression;
export declare type PatternKind = namedTypes.Identifier | namedTypes.RestElement | namedTypes.SpreadElementPattern | namedTypes.PropertyPattern | namedTypes.ObjectPattern | namedTypes.ArrayPattern | namedTypes.AssignmentPattern | namedTypes.SpreadPropertyPattern | namedTypes.JSXIdentifier | namedTypes.PrivateName | namedTypes.TSAsExpression | namedTypes.TSNonNullExpression | namedTypes.TSTypeParameter | namedTypes.TSTypeAssertion | namedTypes.TSParameterProperty;
export declare type IdentifierKind = namedTypes.Identifier | namedTypes.JSXIdentifier | namedTypes.TSTypeParameter;
export declare type BlockStatementKind = namedTypes.BlockStatement;
export declare type EmptyStatementKind = namedTypes.EmptyStatement;
export declare type ExpressionStatementKind = namedTypes.ExpressionStatement;
export declare type IfStatementKind = namedTypes.IfStatement;
export declare type LabeledStatementKind = namedTypes.LabeledStatement;
export declare type BreakStatementKind = namedTypes.BreakStatement;
export declare type ContinueStatementKind = namedTypes.ContinueStatement;
export declare type WithStatementKind = namedTypes.WithStatement;
export declare type SwitchStatementKind = namedTypes.SwitchStatement;
export declare type SwitchCaseKind = namedTypes.SwitchCase;
export declare type ReturnStatementKind = namedTypes.ReturnStatement;
export declare type ThrowStatementKind = namedTypes.ThrowStatement;
export declare type TryStatementKind = namedTypes.TryStatement;
export declare type CatchClauseKind = namedTypes.CatchClause;
export declare type WhileStatementKind = namedTypes.WhileStatement;
export declare type DoWhileStatementKind = namedTypes.DoWhileStatement;
export declare type ForStatementKind = namedTypes.ForStatement;
export declare type DeclarationKind = namedTypes.VariableDeclaration | namedTypes.FunctionDeclaration | namedTypes.MethodDefinition | namedTypes.ClassPropertyDefinition | namedTypes.ClassProperty | namedTypes.ClassBody | namedTypes.ClassDeclaration | namedTypes.ImportDeclaration | namedTypes.TSTypeParameterDeclaration | namedTypes.InterfaceDeclaration | namedTypes.DeclareInterface | namedTypes.TypeAlias | namedTypes.OpaqueType | namedTypes.DeclareTypeAlias | namedTypes.DeclareOpaqueType | namedTypes.DeclareClass | namedTypes.DeclareExportDeclaration | namedTypes.DeclareExportAllDeclaration | namedTypes.ExportDeclaration | namedTypes.ExportDefaultDeclaration | namedTypes.ExportNamedDeclaration | namedTypes.ExportAllDeclaration | namedTypes.ClassPrivateProperty | namedTypes.ClassMethod | namedTypes.ClassPrivateMethod | namedTypes.TSDeclareFunction | namedTypes.TSDeclareMethod | namedTypes.TSIndexSignature | namedTypes.TSPropertySignature | namedTypes.TSMethodSignature | namedTypes.TSCallSignatureDeclaration | namedTypes.TSConstructSignatureDeclaration | namedTypes.TSEnumDeclaration | namedTypes.TSTypeAliasDeclaration | namedTypes.TSModuleDeclaration | namedTypes.TSImportEqualsDeclaration | namedTypes.TSExternalModuleReference | namedTypes.TSNamespaceExportDeclaration | namedTypes.TSInterfaceDeclaration;
export declare type VariableDeclarationKind = namedTypes.VariableDeclaration;
export declare type ForInStatementKind = namedTypes.ForInStatement;
export declare type DebuggerStatementKind = namedTypes.DebuggerStatement;
export declare type FunctionDeclarationKind = namedTypes.FunctionDeclaration;
export declare type FunctionExpressionKind = namedTypes.FunctionExpression;
export declare type VariableDeclaratorKind = namedTypes.VariableDeclarator;
export declare type ThisExpressionKind = namedTypes.ThisExpression;
export declare type ArrayExpressionKind = namedTypes.ArrayExpression;
export declare type ObjectExpressionKind = namedTypes.ObjectExpression;
export declare type PropertyKind = namedTypes.Property;
export declare type LiteralKind = namedTypes.Literal | namedTypes.JSXText | namedTypes.StringLiteral | namedTypes.NumericLiteral | namedTypes.BigIntLiteral | namedTypes.NullLiteral | namedTypes.BooleanLiteral | namedTypes.RegExpLiteral;
export declare type SequenceExpressionKind = namedTypes.SequenceExpression;
export declare type UnaryExpressionKind = namedTypes.UnaryExpression;
export declare type BinaryExpressionKind = namedTypes.BinaryExpression;
export declare type AssignmentExpressionKind = namedTypes.AssignmentExpression;
export declare type MemberExpressionKind = namedTypes.MemberExpression | namedTypes.JSXMemberExpression | namedTypes.OptionalMemberExpression;
export declare type UpdateExpressionKind = namedTypes.UpdateExpression;
export declare type LogicalExpressionKind = namedTypes.LogicalExpression;
export declare type ConditionalExpressionKind = namedTypes.ConditionalExpression;
export declare type NewExpressionKind = namedTypes.NewExpression;
export declare type CallExpressionKind = namedTypes.CallExpression | namedTypes.OptionalCallExpression;
export declare type RestElementKind = namedTypes.RestElement;
export declare type TypeAnnotationKind = namedTypes.TypeAnnotation;
export declare type TSTypeAnnotationKind = namedTypes.TSTypeAnnotation | namedTypes.TSTypePredicate;
export declare type SpreadElementPatternKind = namedTypes.SpreadElementPattern;
export declare type ArrowFunctionExpressionKind = namedTypes.ArrowFunctionExpression;
export declare type ForOfStatementKind = namedTypes.ForOfStatement;
export declare type YieldExpressionKind = namedTypes.YieldExpression;
export declare type GeneratorExpressionKind = namedTypes.GeneratorExpression;
export declare type ComprehensionBlockKind = namedTypes.ComprehensionBlock;
export declare type ComprehensionExpressionKind = namedTypes.ComprehensionExpression;
export declare type ObjectPropertyKind = namedTypes.ObjectProperty;
export declare type PropertyPatternKind = namedTypes.PropertyPattern;
export declare type ObjectPatternKind = namedTypes.ObjectPattern;
export declare type ArrayPatternKind = namedTypes.ArrayPattern;
export declare type MethodDefinitionKind = namedTypes.MethodDefinition;
export declare type SpreadElementKind = namedTypes.SpreadElement;
export declare type AssignmentPatternKind = namedTypes.AssignmentPattern;
export declare type ClassPropertyDefinitionKind = namedTypes.ClassPropertyDefinition;
export declare type ClassPropertyKind = namedTypes.ClassProperty | namedTypes.ClassPrivateProperty;
export declare type ClassBodyKind = namedTypes.ClassBody;
export declare type ClassDeclarationKind = namedTypes.ClassDeclaration;
export declare type ClassExpressionKind = namedTypes.ClassExpression;
export declare type SpecifierKind = namedTypes.ImportSpecifier | namedTypes.ImportNamespaceSpecifier | namedTypes.ImportDefaultSpecifier | namedTypes.ExportSpecifier | namedTypes.ExportBatchSpecifier | namedTypes.ExportNamespaceSpecifier | namedTypes.ExportDefaultSpecifier;
export declare type ModuleSpecifierKind = namedTypes.ImportSpecifier | namedTypes.ImportNamespaceSpecifier | namedTypes.ImportDefaultSpecifier | namedTypes.ExportSpecifier;
export declare type ImportSpecifierKind = namedTypes.ImportSpecifier;
export declare type ImportNamespaceSpecifierKind = namedTypes.ImportNamespaceSpecifier;
export declare type ImportDefaultSpecifierKind = namedTypes.ImportDefaultSpecifier;
export declare type ImportDeclarationKind = namedTypes.ImportDeclaration;
export declare type TaggedTemplateExpressionKind = namedTypes.TaggedTemplateExpression;
export declare type TemplateLiteralKind = namedTypes.TemplateLiteral;
export declare type TemplateElementKind = namedTypes.TemplateElement;
export declare type SpreadPropertyKind = namedTypes.SpreadProperty;
export declare type SpreadPropertyPatternKind = namedTypes.SpreadPropertyPattern;
export declare type AwaitExpressionKind = namedTypes.AwaitExpression;
export declare type ImportExpressionKind = namedTypes.ImportExpression;
export declare type JSXAttributeKind = namedTypes.JSXAttribute;
export declare type JSXIdentifierKind = namedTypes.JSXIdentifier;
export declare type JSXNamespacedNameKind = namedTypes.JSXNamespacedName;
export declare type JSXExpressionContainerKind = namedTypes.JSXExpressionContainer;
export declare type JSXMemberExpressionKind = namedTypes.JSXMemberExpression;
export declare type JSXSpreadAttributeKind = namedTypes.JSXSpreadAttribute;
export declare type JSXElementKind = namedTypes.JSXElement;
export declare type JSXOpeningElementKind = namedTypes.JSXOpeningElement;
export declare type JSXClosingElementKind = namedTypes.JSXClosingElement;
export declare type JSXFragmentKind = namedTypes.JSXFragment;
export declare type JSXTextKind = namedTypes.JSXText;
export declare type JSXOpeningFragmentKind = namedTypes.JSXOpeningFragment;
export declare type JSXClosingFragmentKind = namedTypes.JSXClosingFragment;
export declare type JSXEmptyExpressionKind = namedTypes.JSXEmptyExpression;
export declare type JSXSpreadChildKind = namedTypes.JSXSpreadChild;
export declare type TypeParameterDeclarationKind = namedTypes.TypeParameterDeclaration;
export declare type TSTypeParameterDeclarationKind = namedTypes.TSTypeParameterDeclaration;
export declare type TypeParameterInstantiationKind = namedTypes.TypeParameterInstantiation;
export declare type TSTypeParameterInstantiationKind = namedTypes.TSTypeParameterInstantiation;
export declare type ClassImplementsKind = namedTypes.ClassImplements;
export declare type TSTypeKind = namedTypes.TSExpressionWithTypeArguments | namedTypes.TSTypeReference | namedTypes.TSAnyKeyword | namedTypes.TSBigIntKeyword | namedTypes.TSBooleanKeyword | namedTypes.TSNeverKeyword | namedTypes.TSNullKeyword | namedTypes.TSNumberKeyword | namedTypes.TSObjectKeyword | namedTypes.TSStringKeyword | namedTypes.TSSymbolKeyword | namedTypes.TSUndefinedKeyword | namedTypes.TSUnknownKeyword | namedTypes.TSVoidKeyword | namedTypes.TSThisType | namedTypes.TSArrayType | namedTypes.TSLiteralType | namedTypes.TSUnionType | namedTypes.TSIntersectionType | namedTypes.TSConditionalType | namedTypes.TSInferType | namedTypes.TSParenthesizedType | namedTypes.TSFunctionType | namedTypes.TSConstructorType | namedTypes.TSMappedType | namedTypes.TSTupleType | namedTypes.TSNamedTupleMember | namedTypes.TSRestType | namedTypes.TSOptionalType | namedTypes.TSIndexedAccessType | namedTypes.TSTypeOperator | namedTypes.TSTypePredicate | namedTypes.TSTypeQuery | namedTypes.TSImportType | namedTypes.TSTypeLiteral;
export declare type TSHasOptionalTypeParameterInstantiationKind = namedTypes.TSExpressionWithTypeArguments | namedTypes.TSTypeReference | namedTypes.TSImportType;
export declare type TSExpressionWithTypeArgumentsKind = namedTypes.TSExpressionWithTypeArguments;
export declare type FlowKind = namedTypes.AnyTypeAnnotation | namedTypes.EmptyTypeAnnotation | namedTypes.MixedTypeAnnotation | namedTypes.VoidTypeAnnotation | namedTypes.NumberTypeAnnotation | namedTypes.NumberLiteralTypeAnnotation | namedTypes.NumericLiteralTypeAnnotation | namedTypes.StringTypeAnnotation | namedTypes.StringLiteralTypeAnnotation | namedTypes.BooleanTypeAnnotation | namedTypes.BooleanLiteralTypeAnnotation | namedTypes.NullableTypeAnnotation | namedTypes.NullLiteralTypeAnnotation | namedTypes.NullTypeAnnotation | namedTypes.ThisTypeAnnotation | namedTypes.ExistsTypeAnnotation | namedTypes.ExistentialTypeParam | namedTypes.FunctionTypeAnnotation | namedTypes.ArrayTypeAnnotation | namedTypes.ObjectTypeAnnotation | namedTypes.GenericTypeAnnotation | namedTypes.MemberTypeAnnotation | namedTypes.UnionTypeAnnotation | namedTypes.IntersectionTypeAnnotation | namedTypes.TypeofTypeAnnotation | namedTypes.TypeParameter | namedTypes.InterfaceTypeAnnotation | namedTypes.TupleTypeAnnotation | namedTypes.InferredPredicate | namedTypes.DeclaredPredicate;
export declare type FlowTypeKind = namedTypes.AnyTypeAnnotation | namedTypes.EmptyTypeAnnotation | namedTypes.MixedTypeAnnotation | namedTypes.VoidTypeAnnotation | namedTypes.NumberTypeAnnotation | namedTypes.NumberLiteralTypeAnnotation | namedTypes.NumericLiteralTypeAnnotation | namedTypes.StringTypeAnnotation | namedTypes.StringLiteralTypeAnnotation | namedTypes.BooleanTypeAnnotation | namedTypes.BooleanLiteralTypeAnnotation | namedTypes.NullableTypeAnnotation | namedTypes.NullLiteralTypeAnnotation | namedTypes.NullTypeAnnotation | namedTypes.ThisTypeAnnotation | namedTypes.ExistsTypeAnnotation | namedTypes.ExistentialTypeParam | namedTypes.FunctionTypeAnnotation | namedTypes.ArrayTypeAnnotation | namedTypes.ObjectTypeAnnotation | namedTypes.GenericTypeAnnotation | namedTypes.MemberTypeAnnotation | namedTypes.UnionTypeAnnotation | namedTypes.IntersectionTypeAnnotation | namedTypes.TypeofTypeAnnotation | namedTypes.TypeParameter | namedTypes.InterfaceTypeAnnotation | namedTypes.TupleTypeAnnotation;
export declare type AnyTypeAnnotationKind = namedTypes.AnyTypeAnnotation;
export declare type EmptyTypeAnnotationKind = namedTypes.EmptyTypeAnnotation;
export declare type MixedTypeAnnotationKind = namedTypes.MixedTypeAnnotation;
export declare type VoidTypeAnnotationKind = namedTypes.VoidTypeAnnotation;
export declare type NumberTypeAnnotationKind = namedTypes.NumberTypeAnnotation;
export declare type NumberLiteralTypeAnnotationKind = namedTypes.NumberLiteralTypeAnnotation;
export declare type NumericLiteralTypeAnnotationKind = namedTypes.NumericLiteralTypeAnnotation;
export declare type StringTypeAnnotationKind = namedTypes.StringTypeAnnotation;
export declare type StringLiteralTypeAnnotationKind = namedTypes.StringLiteralTypeAnnotation;
export declare type BooleanTypeAnnotationKind = namedTypes.BooleanTypeAnnotation;
export declare type BooleanLiteralTypeAnnotationKind = namedTypes.BooleanLiteralTypeAnnotation;
export declare type NullableTypeAnnotationKind = namedTypes.NullableTypeAnnotation;
export declare type NullLiteralTypeAnnotationKind = namedTypes.NullLiteralTypeAnnotation;
export declare type NullTypeAnnotationKind = namedTypes.NullTypeAnnotation;
export declare type ThisTypeAnnotationKind = namedTypes.ThisTypeAnnotation;
export declare type ExistsTypeAnnotationKind = namedTypes.ExistsTypeAnnotation;
export declare type ExistentialTypeParamKind = namedTypes.ExistentialTypeParam;
export declare type FunctionTypeAnnotationKind = namedTypes.FunctionTypeAnnotation;
export declare type FunctionTypeParamKind = namedTypes.FunctionTypeParam;
export declare type ArrayTypeAnnotationKind = namedTypes.ArrayTypeAnnotation;
export declare type ObjectTypeAnnotationKind = namedTypes.ObjectTypeAnnotation;
export declare type ObjectTypePropertyKind = namedTypes.ObjectTypeProperty;
export declare type ObjectTypeSpreadPropertyKind = namedTypes.ObjectTypeSpreadProperty;
export declare type ObjectTypeIndexerKind = namedTypes.ObjectTypeIndexer;
export declare type ObjectTypeCallPropertyKind = namedTypes.ObjectTypeCallProperty;
export declare type ObjectTypeInternalSlotKind = namedTypes.ObjectTypeInternalSlot;
export declare type VarianceKind = namedTypes.Variance;
export declare type QualifiedTypeIdentifierKind = namedTypes.QualifiedTypeIdentifier;
export declare type GenericTypeAnnotationKind = namedTypes.GenericTypeAnnotation;
export declare type MemberTypeAnnotationKind = namedTypes.MemberTypeAnnotation;
export declare type UnionTypeAnnotationKind = namedTypes.UnionTypeAnnotation;
export declare type IntersectionTypeAnnotationKind = namedTypes.IntersectionTypeAnnotation;
export declare type TypeofTypeAnnotationKind = namedTypes.TypeofTypeAnnotation;
export declare type TypeParameterKind = namedTypes.TypeParameter;
export declare type InterfaceTypeAnnotationKind = namedTypes.InterfaceTypeAnnotation;
export declare type InterfaceExtendsKind = namedTypes.InterfaceExtends;
export declare type InterfaceDeclarationKind = namedTypes.InterfaceDeclaration | namedTypes.DeclareInterface | namedTypes.DeclareClass;
export declare type DeclareInterfaceKind = namedTypes.DeclareInterface;
export declare type TypeAliasKind = namedTypes.TypeAlias | namedTypes.DeclareTypeAlias | namedTypes.DeclareOpaqueType;
export declare type OpaqueTypeKind = namedTypes.OpaqueType;
export declare type DeclareTypeAliasKind = namedTypes.DeclareTypeAlias;
export declare type DeclareOpaqueTypeKind = namedTypes.DeclareOpaqueType;
export declare type TypeCastExpressionKind = namedTypes.TypeCastExpression;
export declare type TupleTypeAnnotationKind = namedTypes.TupleTypeAnnotation;
export declare type DeclareVariableKind = namedTypes.DeclareVariable;
export declare type DeclareFunctionKind = namedTypes.DeclareFunction;
export declare type DeclareClassKind = namedTypes.DeclareClass;
export declare type DeclareModuleKind = namedTypes.DeclareModule;
export declare type DeclareModuleExportsKind = namedTypes.DeclareModuleExports;
export declare type DeclareExportDeclarationKind = namedTypes.DeclareExportDeclaration;
export declare type ExportSpecifierKind = namedTypes.ExportSpecifier;
export declare type ExportBatchSpecifierKind = namedTypes.ExportBatchSpecifier;
export declare type DeclareExportAllDeclarationKind = namedTypes.DeclareExportAllDeclaration;
export declare type FlowPredicateKind = namedTypes.InferredPredicate | namedTypes.DeclaredPredicate;
export declare type InferredPredicateKind = namedTypes.InferredPredicate;
export declare type DeclaredPredicateKind = namedTypes.DeclaredPredicate;
export declare type ExportDeclarationKind = namedTypes.ExportDeclaration;
export declare type BlockKind = namedTypes.Block;
export declare type LineKind = namedTypes.Line;
export declare type NoopKind = namedTypes.Noop;
export declare type DoExpressionKind = namedTypes.DoExpression;
export declare type SuperKind = namedTypes.Super;
export declare type BindExpressionKind = namedTypes.BindExpression;
export declare type DecoratorKind = namedTypes.Decorator;
export declare type MetaPropertyKind = namedTypes.MetaProperty;
export declare type ParenthesizedExpressionKind = namedTypes.ParenthesizedExpression;
export declare type ExportDefaultDeclarationKind = namedTypes.ExportDefaultDeclaration;
export declare type ExportNamedDeclarationKind = namedTypes.ExportNamedDeclaration;
export declare type ExportNamespaceSpecifierKind = namedTypes.ExportNamespaceSpecifier;
export declare type ExportDefaultSpecifierKind = namedTypes.ExportDefaultSpecifier;
export declare type ExportAllDeclarationKind = namedTypes.ExportAllDeclaration;
export declare type CommentBlockKind = namedTypes.CommentBlock;
export declare type CommentLineKind = namedTypes.CommentLine;
export declare type DirectiveKind = namedTypes.Directive;
export declare type DirectiveLiteralKind = namedTypes.DirectiveLiteral;
export declare type InterpreterDirectiveKind = namedTypes.InterpreterDirective;
export declare type StringLiteralKind = namedTypes.StringLiteral;
export declare type NumericLiteralKind = namedTypes.NumericLiteral;
export declare type BigIntLiteralKind = namedTypes.BigIntLiteral;
export declare type NullLiteralKind = namedTypes.NullLiteral;
export declare type BooleanLiteralKind = namedTypes.BooleanLiteral;
export declare type RegExpLiteralKind = namedTypes.RegExpLiteral;
export declare type ObjectMethodKind = namedTypes.ObjectMethod;
export declare type ClassPrivatePropertyKind = namedTypes.ClassPrivateProperty;
export declare type ClassMethodKind = namedTypes.ClassMethod;
export declare type ClassPrivateMethodKind = namedTypes.ClassPrivateMethod;
export declare type PrivateNameKind = namedTypes.PrivateName;
export declare type RestPropertyKind = namedTypes.RestProperty;
export declare type ForAwaitStatementKind = namedTypes.ForAwaitStatement;
export declare type ImportKind = namedTypes.Import;
export declare type TSQualifiedNameKind = namedTypes.TSQualifiedName;
export declare type TSTypeReferenceKind = namedTypes.TSTypeReference;
export declare type TSHasOptionalTypeParametersKind = namedTypes.TSFunctionType | namedTypes.TSConstructorType | namedTypes.TSDeclareFunction | namedTypes.TSDeclareMethod | namedTypes.TSMethodSignature | namedTypes.TSCallSignatureDeclaration | namedTypes.TSConstructSignatureDeclaration | namedTypes.TSTypeAliasDeclaration | namedTypes.TSInterfaceDeclaration;
export declare type TSHasOptionalTypeAnnotationKind = namedTypes.TSFunctionType | namedTypes.TSConstructorType | namedTypes.TSIndexSignature | namedTypes.TSPropertySignature | namedTypes.TSMethodSignature | namedTypes.TSCallSignatureDeclaration | namedTypes.TSConstructSignatureDeclaration;
export declare type TSAsExpressionKind = namedTypes.TSAsExpression;
export declare type TSNonNullExpressionKind = namedTypes.TSNonNullExpression;
export declare type TSAnyKeywordKind = namedTypes.TSAnyKeyword;
export declare type TSBigIntKeywordKind = namedTypes.TSBigIntKeyword;
export declare type TSBooleanKeywordKind = namedTypes.TSBooleanKeyword;
export declare type TSNeverKeywordKind = namedTypes.TSNeverKeyword;
export declare type TSNullKeywordKind = namedTypes.TSNullKeyword;
export declare type TSNumberKeywordKind = namedTypes.TSNumberKeyword;
export declare type TSObjectKeywordKind = namedTypes.TSObjectKeyword;
export declare type TSStringKeywordKind = namedTypes.TSStringKeyword;
export declare type TSSymbolKeywordKind = namedTypes.TSSymbolKeyword;
export declare type TSUndefinedKeywordKind = namedTypes.TSUndefinedKeyword;
export declare type TSUnknownKeywordKind = namedTypes.TSUnknownKeyword;
export declare type TSVoidKeywordKind = namedTypes.TSVoidKeyword;
export declare type TSThisTypeKind = namedTypes.TSThisType;
export declare type TSArrayTypeKind = namedTypes.TSArrayType;
export declare type TSLiteralTypeKind = namedTypes.TSLiteralType;
export declare type TSUnionTypeKind = namedTypes.TSUnionType;
export declare type TSIntersectionTypeKind = namedTypes.TSIntersectionType;
export declare type TSConditionalTypeKind = namedTypes.TSConditionalType;
export declare type TSInferTypeKind = namedTypes.TSInferType;
export declare type TSTypeParameterKind = namedTypes.TSTypeParameter;
export declare type TSParenthesizedTypeKind = namedTypes.TSParenthesizedType;
export declare type TSFunctionTypeKind = namedTypes.TSFunctionType;
export declare type TSConstructorTypeKind = namedTypes.TSConstructorType;
export declare type TSDeclareFunctionKind = namedTypes.TSDeclareFunction;
export declare type TSDeclareMethodKind = namedTypes.TSDeclareMethod;
export declare type TSMappedTypeKind = namedTypes.TSMappedType;
export declare type TSTupleTypeKind = namedTypes.TSTupleType;
export declare type TSNamedTupleMemberKind = namedTypes.TSNamedTupleMember;
export declare type TSRestTypeKind = namedTypes.TSRestType;
export declare type TSOptionalTypeKind = namedTypes.TSOptionalType;
export declare type TSIndexedAccessTypeKind = namedTypes.TSIndexedAccessType;
export declare type TSTypeOperatorKind = namedTypes.TSTypeOperator;
export declare type TSIndexSignatureKind = namedTypes.TSIndexSignature;
export declare type TSPropertySignatureKind = namedTypes.TSPropertySignature;
export declare type TSMethodSignatureKind = namedTypes.TSMethodSignature;
export declare type TSTypePredicateKind = namedTypes.TSTypePredicate;
export declare type TSCallSignatureDeclarationKind = namedTypes.TSCallSignatureDeclaration;
export declare type TSConstructSignatureDeclarationKind = namedTypes.TSConstructSignatureDeclaration;
export declare type TSEnumMemberKind = namedTypes.TSEnumMember;
export declare type TSTypeQueryKind = namedTypes.TSTypeQuery;
export declare type TSImportTypeKind = namedTypes.TSImportType;
export declare type TSTypeLiteralKind = namedTypes.TSTypeLiteral;
export declare type TSTypeAssertionKind = namedTypes.TSTypeAssertion;
export declare type TSEnumDeclarationKind = namedTypes.TSEnumDeclaration;
export declare type TSTypeAliasDeclarationKind = namedTypes.TSTypeAliasDeclaration;
export declare type TSModuleBlockKind = namedTypes.TSModuleBlock;
export declare type TSModuleDeclarationKind = namedTypes.TSModuleDeclaration;
export declare type TSImportEqualsDeclarationKind = namedTypes.TSImportEqualsDeclaration;
export declare type TSExternalModuleReferenceKind = namedTypes.TSExternalModuleReference;
export declare type TSExportAssignmentKind = namedTypes.TSExportAssignment;
export declare type TSNamespaceExportDeclarationKind = namedTypes.TSNamespaceExportDeclaration;
export declare type TSInterfaceBodyKind = namedTypes.TSInterfaceBody;
export declare type TSInterfaceDeclarationKind = namedTypes.TSInterfaceDeclaration;
export declare type TSParameterPropertyKind = namedTypes.TSParameterProperty;
export declare type OptionalMemberExpressionKind = namedTypes.OptionalMemberExpression;
export declare type OptionalCallExpressionKind = namedTypes.OptionalCallExpression;
