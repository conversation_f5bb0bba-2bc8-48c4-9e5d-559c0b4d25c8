{"name": "bare-stream", "version": "2.6.5", "description": "Streaming data for JavaScript", "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./package": "./package.json", "./promises": "./promises.js", "./web": "./web.js", "./global": "./global.js"}, "files": ["index.js", "index.d.ts", "promises.js", "web.js", "global.js"], "scripts": {"test": "prettier . --check && bare test.js"}, "repository": {"type": "git", "url": "git+https://github.com/holepunchto/bare-stream.git"}, "author": "Holepunch", "license": "Apache-2.0", "bugs": {"url": "https://github.com/holepunchto/bare-stream/issues"}, "homepage": "https://github.com/holepunchto/bare-stream#readme", "dependencies": {"streamx": "^2.21.0"}, "devDependencies": {"bare-buffer": "^3.0.0", "bare-events": "^2.5.4", "brittle": "^3.5.2", "prettier": "^3.3.3", "prettier-config-standard": "^7.0.0"}, "peerDependencies": {"bare-buffer": "*", "bare-events": "*"}, "peerDependenciesMeta": {"bare-buffer": {"optional": true}, "bare-events": {"optional": true}}}