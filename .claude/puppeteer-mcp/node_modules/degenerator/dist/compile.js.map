{"version": 3, "file": "compile.js", "sourceRoot": "", "sources": ["../src/compile.ts"], "names": [], "mappings": ";;;AAAA,+BAA6B;AAC7B,+CAA4C;AAe5C,SAAgB,OAAO,CACtB,GAAsB,EACtB,IAAY,EACZ,UAAkB,EAClB,UAA0B,EAAE;IAE5B,MAAM,QAAQ,GAAG,IAAA,yBAAW,EAAC,IAAI,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;IAExD,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC;IAE5B,0BAA0B;IAC1B,IAAI,OAAO,CAAC,OAAO,EAAE;QACpB,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC5D,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;gBAChC,MAAM,IAAI,KAAK,CACd,gDAAgD,IAAI,gBAAgB,OAAO,KAAK,GAAG,CACnF,CAAC;aACF;YACD,MAAM,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE;gBACjD,MAAM,MAAM,GAAG,KAAK,CACnB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,mBAAmB,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAClD,CAAC;gBACF,EAAE,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBAChC,OAAO,mBAAmB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YACH,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;SAClE;KACD;IAED,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,QAAQ,IAAI,UAAU,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC5E,MAAM,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IAErC,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IACxB,IAAI,CAAC,KAAK,UAAU,EAAE;QACrB,MAAM,IAAI,KAAK,CACd,iCAAiC,UAAU,8BAA8B,CAAC,GAAG,CAC7E,CAAC;KACF;IACD,MAAM,CAAC,GAAG,KAAK,WAAW,GAAG,IAAO;QACnC,IAAI,aAAwC,CAAC;QAC7C,IAAI,cAAyC,CAAC;QAC9C,IAAI;YACH,MAAM,MAAM,GAAG,EAAE,CAAC,YAAY,CAC7B,EAAE,EACF,EAAE,CAAC,SAAS,EACZ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,mBAAmB,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAClD,CAAC;YACF,aAAa,GAAG,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YACxC,MAAM,eAAe,GAAG,EAAE,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YACzD,EAAE,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAChC,MAAM,cAAc,GAAG,MAAM,eAAe,CAAC;YAC7C,cAAc,GAAG,EAAE,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YACjD,OAAO,mBAAmB,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;SAC/C;QAAC,OAAO,GAAY,EAAE;YACtB,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,IAAI,GAAG,IAAI,GAAG,CAAC,KAAK,EAAE;gBAClE,IACC,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ;oBAC7B,OAAO,IAAI,GAAG,CAAC,KAAK;oBACpB,MAAM,IAAI,GAAG,CAAC,KAAK;oBACnB,SAAS,IAAI,GAAG,CAAC,KAAK;oBACtB,OAAO,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,QAAQ;oBACnC,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ;oBAClC,OAAO,GAAG,CAAC,KAAK,CAAC,OAAO,KAAK,QAAQ,EACpC;oBACD,oDAAoD;oBACpD,oDAAoD;oBACpD,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,OAAO,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;iBAChF;gBACD,MAAM,GAAG,CAAC,KAAK,CAAC;aAChB;YACD,MAAM,GAAG,CAAC;SACV;gBAAS;YACT,aAAa,EAAE,OAAO,EAAE,CAAC;YACzB,cAAc,EAAE,OAAO,EAAE,CAAC;SAC1B;IACF,CAAC,CAAC;IACF,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE,UAAU,EAAE;QACpC,KAAK,EAAE,GAAG,EAAE,CAAC,QAAQ;QACrB,UAAU,EAAE,KAAK;KACjB,CAAC,CAAC;IACH,OAAO,CAAC,CAAC;AACV,CAAC;AAjFD,0BAiFC;AAED,SAAS,mBAAmB,CAAC,EAAkB,EAAE,GAAkB;IAClE,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrB,CAAC;AAED,SAAS,mBAAmB,CAAC,EAAkB,EAAE,GAAY;IAC5D,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;QAC/B,OAAO,EAAE,CAAC,SAAS,CAAC;KACpB;SAAM,IAAI,GAAG,KAAK,IAAI,EAAE;QACxB,OAAO,EAAE,CAAC,IAAI,CAAC;KACf;SAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QACnC,OAAO,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;KACzB;SAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QACnC,OAAO,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;KACzB;SAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QACnC,OAAO,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;KACzB;SAAM,IAAI,OAAO,GAAG,KAAK,SAAS,EAAE;QACpC,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC;KAChC;SAAM,IAAI,YAAK,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;QAChC,MAAM,OAAO,GAAG,EAAE,CAAC,UAAU,EAAE,CAAC;QAChC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACpD,GAAG,CAAC,IAAI,CACP,CAAC,CAAU,EAAE,EAAE;YACd,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,EACD,CAAC,GAAY,EAAE,EAAE;YAChB,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;QAC9C,CAAC,CACD,CAAC;QACF,OAAO,OAAO,CAAC,MAAM,CAAC;KACtB;SAAM,IAAI,YAAK,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;QACpC,OAAO,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;KACxB;IACD,MAAM,IAAI,KAAK,CAAC,sBAAsB,GAAG,EAAE,CAAC,CAAC;AAC9C,CAAC"}