# Model Context Protocol servers (ARCHIVED)

⚠️ **IMPORTANT: This repository is archived and no longer maintained.**

## Looking for MCP servers?

This repository contains archived reference implementations that are no longer maintained.

**For the current, actively maintained MCP servers, please visit:**

### 👉 [https://github.com/modelcontextprotocol/servers](https://github.com/modelcontextprotocol/servers)

## About this Archive

This repository contains historical reference implementations for the [Model Context Protocol](https://modelcontextprotocol.io/) (MCP). These servers were created to demonstrate MCP features and SDK capabilities but are now archived.

### ⚠️ Security Notice

**NO SECURITY GUARANTEES ARE PROVIDED FOR THESE ARCHIVED SERVERS.**

- These servers are no longer maintained
- No security updates or bug fixes will be provided
- Use at your own risk
- For production use, please refer to the actively maintained servers repository linked above

## Archived Servers

The following servers are included in this archive for historical reference:

- AWS KB Retrieval
- Brave Search
- EverArt
- Git
- GitHub
- GitLab
- Google Drive
- Google Maps
- PostgreSQL
- Puppeteer
- Redis
- Sentry
- Slack
- SQLite

## Contributing

This repository is archived and no longer accepts contributions. For contributing to MCP servers, please visit the [active servers repository](https://github.com/modelcontextprotocol/servers).

## License

MIT