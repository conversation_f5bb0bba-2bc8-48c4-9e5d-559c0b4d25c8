{"name": "@modelcontextprotocol/server-brave-search", "version": "0.6.2", "description": "MCP server for Brave Search API integration", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/servers/issues", "type": "module", "bin": {"mcp-server-brave-search": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "1.0.1"}, "devDependencies": {"@types/node": "^22", "shx": "^0.3.4", "typescript": "^5.6.2"}}