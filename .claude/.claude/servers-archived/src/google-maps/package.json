{"name": "@modelcontextprotocol/server-google-maps", "version": "0.6.2", "description": "MCP server for using the Google Maps API", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/servers/issues", "type": "module", "bin": {"mcp-server-google-maps": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "1.0.1", "@types/node-fetch": "^2.6.12", "node-fetch": "^3.3.2"}, "devDependencies": {"shx": "^0.3.4", "typescript": "^5.6.2"}}