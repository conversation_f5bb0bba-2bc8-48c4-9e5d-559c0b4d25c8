{"name": "@modelcontextprotocol/server-redis", "version": "0.1.1", "description": "MCP server for using Redis", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/servers/issues", "type": "module", "bin": {"mcp-server-redis": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "@types/node": "^22.10.2", "@types/redis": "^4.0.10", "redis": "^4.7.0"}, "devDependencies": {"shx": "^0.3.4", "typescript": "^5.7.2"}}