{"name": "@modelcontextprotocol/server-everart", "version": "0.6.2", "description": "MCP server for EverArt API integration", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/servers/issues", "type": "module", "bin": {"mcp-server-everart": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "0.5.0", "everart": "^1.0.0", "node-fetch": "^3.3.2", "open": "^9.1.0"}, "devDependencies": {"@types/node": "^22", "shx": "^0.3.4", "typescript": "^5.3.3"}}