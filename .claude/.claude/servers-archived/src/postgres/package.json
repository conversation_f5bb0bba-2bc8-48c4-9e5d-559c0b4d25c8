{"name": "@modelcontextprotocol/server-postgres", "version": "0.6.2", "description": "MCP server for interacting with PostgreSQL databases", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/servers/issues", "type": "module", "bin": {"mcp-server-postgres": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "1.0.1", "pg": "^8.13.0"}, "devDependencies": {"@types/pg": "^8.11.10", "shx": "^0.3.4", "typescript": "^5.6.2"}}