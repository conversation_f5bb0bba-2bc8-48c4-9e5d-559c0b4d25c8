[project]
name = "mcp-server-sentry"
version = "0.6.2"
description = "MCP server for retrieving issues from sentry.io"
readme = "README.md"
requires-python = ">=3.10"
dependencies = ["mcp>=1.0.0"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = ["pyright>=1.1.389", "pytest>=8.3.3", "ruff>=0.8.0"]

[project.scripts]
mcp-server-sentry = "mcp_server_sentry:main"
