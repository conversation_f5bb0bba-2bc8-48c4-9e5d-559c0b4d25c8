{"name": "@modelcontextprotocol/server-puppeteer", "version": "0.6.2", "description": "MCP server for browser automation using Puppeteer", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/servers/issues", "type": "module", "bin": {"mcp-server-puppeteer": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "1.0.1", "puppeteer": "^23.4.0"}, "devDependencies": {"shx": "^0.3.4", "typescript": "^5.6.2"}}