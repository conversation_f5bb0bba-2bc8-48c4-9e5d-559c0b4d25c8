{"name": "@modelcontextprotocol/server-gitlab", "version": "0.6.2", "description": "MCP server for using the GitLab API", "license": "MIT", "author": "GitLab, PBC (https://gitlab.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/servers/issues", "type": "module", "bin": {"mcp-server-gitlab": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "1.0.1", "@types/node-fetch": "^2.6.12", "node-fetch": "^3.3.2", "zod-to-json-schema": "^3.23.5"}, "devDependencies": {"shx": "^0.3.4", "typescript": "^5.6.2"}}